"use client"

import { ReactNode } from 'react'
import { Navigation } from '@/components/Navigation'
import { Footer } from '@/components/Footer'

interface PageLayoutProps {
  children: ReactNode
  className?: string
  showFooter?: boolean
  containerClassName?: string
}

export function PageLayout({ 
  children, 
  className = "", 
  showFooter = true,
  containerClassName = "container mx-auto px-4 py-8"
}: PageLayoutProps) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>
      {/* Header Navigation - موحد عبر جميع الصفحات */}
      <Navigation />
      
      {/* Main Content */}
      <main className={containerClassName}>
        {children}
      </main>
      
      {/* Footer - اختياري */}
      {showFooter && <Footer />}
    </div>
  )
}

// Layout خاص بلوحات التحكم
export function DashboardLayout({ 
  children, 
  className = "",
  title,
  description 
}: PageLayoutProps & { title?: string; description?: string }) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>
      <Navigation />
      
      <main className="container mx-auto px-4 py-8">
        {(title || description) && (
          <div className="mb-8">
            {title && (
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text mb-2">
                {title}
              </h1>
            )}
            {description && (
              <p className="text-gray-600 dark:text-gray-300 arabic-text">
                {description}
              </p>
            )}
          </div>
        )}
        {children}
      </main>
    </div>
  )
}

// Layout للصفحات الخاصة (مثل 404، خطأ)
export function ErrorLayout({ children, className = "" }: PageLayoutProps) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>
      <Navigation />
      <main className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[calc(100vh-200px)]">
        {children}
      </main>
    </div>
  )
}
