{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/mockData.ts"], "sourcesContent": ["// بيانات وهمية للتطوير والاختبار\nimport { AIModel, AISubModel, ModelActivity } from '@/types/ai-models'\nimport { PageTemplate, PageProject, ComponentLibraryItem, PageComponent } from '@/types/page-builder'\n\nexport interface MockPage {\n  id: string\n  slug: string\n  is_published: boolean\n  author_id: string\n  featured_image?: string\n  created_at: string\n  updated_at: string\n  page_content: MockPageContent[]\n  profiles?: {\n    full_name: string\n  }\n}\n\nexport interface MockPageContent {\n  id: string\n  page_id: string\n  language: 'ar' | 'en' | 'fr'\n  title: string\n  content: string\n  meta_description?: string\n  meta_keywords?: string\n}\n\nexport interface MockMenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockCategory {\n  id: string\n  name_ar: string\n  name_en?: string\n  name_fr?: string\n  slug: string\n  icon?: string\n  description?: string\n  is_active: boolean\n  order_index: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockProduct {\n  id: string\n  name: string\n  description: string\n  category: string // تغيير من union type إلى string للمرونة\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n  features?: string[]\n  specifications?: Record<string, any>\n}\n\nexport interface MockSchool {\n  id: string\n  admin_id?: string\n  name: string\n  name_en?: string\n  name_fr?: string\n  address?: string\n  city?: string\n  phone?: string\n  email?: string\n  website?: string\n  logo_url?: string\n  graduation_date?: string\n  student_count: number\n  is_active: boolean\n  settings?: Record<string, any>\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockOrder {\n  id: string\n  order_number: string\n  customer_id: string\n  customer_name: string\n  customer_email: string\n  customer_phone?: string\n  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'\n  items: MockOrderItem[]\n  subtotal: number\n  tax: number\n  shipping_cost: number\n  total: number\n  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'\n  payment_method?: string\n  shipping_address: {\n    street: string\n    city: string\n    state: string\n    postal_code: string\n    country: string\n  }\n  tracking_number?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  delivery_date?: string\n  school_id?: string\n  school_name?: string\n}\n\nexport interface MockOrderItem {\n  id: string\n  order_id: string\n  product_id: string\n  product_name: string\n  product_image: string\n  category: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  customizations?: {\n    color?: string\n    size?: string\n    embroidery?: string\n    special_requests?: string\n  }\n}\n\n// بيانات وهمية للصفحات\nexport const mockPages: MockPage[] = [\n  {\n    id: '1',\n    slug: 'about-us',\n    is_published: true,\n    author_id: 'admin-1',\n    featured_image: '/images/about-hero.jpg',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '1-ar',\n        page_id: '1',\n        language: 'ar',\n        title: 'من نحن',\n        content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n        meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n        meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n      },\n      {\n        id: '1-en',\n        page_id: '1',\n        language: 'en',\n        title: 'About Us',\n        content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n        meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n        meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n      }\n    ]\n  },\n  {\n    id: '2',\n    slug: 'services',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '2-ar',\n        page_id: '2',\n        language: 'ar',\n        title: 'خدماتنا',\n        content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n        meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n        meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n      }\n    ]\n  },\n  {\n    id: '3',\n    slug: 'contact',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-17T11:00:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '3-ar',\n        page_id: '3',\n        language: 'ar',\n        title: 'اتصل بنا',\n        content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n        meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n        meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n      }\n    ]\n  }\n]\n\n// بيانات وهمية للقوائم\nexport const mockMenuItems: MockMenuItem[] = [\n  {\n    id: '1',\n    title_ar: 'الرئيسية',\n    title_en: 'Home',\n    title_fr: 'Accueil',\n    slug: 'home',\n    icon: 'Home',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    title_ar: 'من نحن',\n    title_en: 'About Us',\n    title_fr: 'À propos',\n    slug: 'about',\n    icon: 'Info',\n    order_index: 2,\n    is_active: true,\n    target_type: 'page',\n    target_value: '1',\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    title_ar: 'خدماتنا',\n    title_en: 'Services',\n    title_fr: 'Services',\n    slug: 'services',\n    icon: 'Settings',\n    order_index: 3,\n    is_active: true,\n    target_type: 'page',\n    target_value: '2',\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    title_ar: 'المنتجات',\n    title_en: 'Products',\n    title_fr: 'Produits',\n    slug: 'products',\n    icon: 'Package',\n    order_index: 4,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products',\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    title_ar: 'تأجير الأزياء',\n    title_en: 'Rental',\n    title_fr: 'Location',\n    slug: 'rental',\n    parent_id: '4',\n    icon: 'Calendar',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=rental',\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  },\n  {\n    id: '6',\n    title_ar: 'بيع الأزياء',\n    title_en: 'Sales',\n    title_fr: 'Vente',\n    slug: 'sales',\n    parent_id: '4',\n    icon: 'ShoppingCart',\n    order_index: 2,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=sale',\n    created_at: '2024-01-15T10:25:00Z',\n    updated_at: '2024-01-15T10:25:00Z'\n  },\n  {\n    id: '7',\n    title_ar: 'الكتالوج',\n    title_en: 'Catalog',\n    title_fr: 'Catalogue',\n    slug: 'catalog',\n    icon: 'Grid3X3',\n    order_index: 5,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/catalog',\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:30:00Z'\n  },\n  {\n    id: '8',\n    title_ar: 'اتصل بنا',\n    title_en: 'Contact',\n    title_fr: 'Contact',\n    slug: 'contact',\n    icon: 'Phone',\n    order_index: 6,\n    is_active: true,\n    target_type: 'page',\n    target_value: '3',\n    created_at: '2024-01-15T10:35:00Z',\n    updated_at: '2024-01-15T10:35:00Z'\n  }\n]\n\n// بيانات وهمية للفئات\nexport const mockCategories: MockCategory[] = [\n  {\n    id: '1',\n    name_ar: 'أثواب التخرج',\n    name_en: 'Graduation Gowns',\n    name_fr: 'Robes de Graduation',\n    slug: 'gown',\n    icon: '👘',\n    description: 'أثواب التخرج الأكاديمية التقليدية',\n    is_active: true,\n    order_index: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    name_ar: 'قبعات التخرج',\n    name_en: 'Graduation Caps',\n    name_fr: 'Chapeaux de Graduation',\n    slug: 'cap',\n    icon: '🎩',\n    description: 'قبعات التخرج الأكاديمية',\n    is_active: true,\n    order_index: 2,\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    name_ar: 'شرابات التخرج',\n    name_en: 'Graduation Tassels',\n    name_fr: 'Glands de Graduation',\n    slug: 'tassel',\n    icon: '🏷️',\n    description: 'شرابات التخرج الملونة',\n    is_active: true,\n    order_index: 3,\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    name_ar: 'أوشحة التخرج',\n    name_en: 'Graduation Stoles',\n    name_fr: 'Étoles de Graduation',\n    slug: 'stole',\n    icon: '🧣',\n    description: 'أوشحة التخرج المميزة',\n    is_active: true,\n    order_index: 4,\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    name_ar: 'القلانس الأكاديمية',\n    name_en: 'Academic Hoods',\n    name_fr: 'Capuches Académiques',\n    slug: 'hood',\n    icon: '🎓',\n    description: 'القلانس الأكاديمية للدرجات العليا',\n    is_active: true,\n    order_index: 5,\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  }\n]\n\n// بيانات وهمية للمنتجات\nexport const mockProducts: MockProduct[] = [\n  {\n    id: '1',\n    name: 'ثوب التخرج الكلاسيكي',\n    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n    category: 'gown',\n    price: 299.99,\n    rental_price: 99.99,\n    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],\n    sizes: ['S', 'M', 'L', 'XL', 'XXL'],\n    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],\n    stock_quantity: 25,\n    is_available: true,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    rating: 4.8,\n    reviews_count: 42,\n    features: ['مقاوم للتجاعيد', 'قابل للغسيل', 'خامة عالية الجودة'],\n    specifications: {\n      material: 'بوليستر عالي الجودة',\n      weight: '0.8 كيلو',\n      care: 'غسيل جاف أو غسيل عادي'\n    }\n  },\n  {\n    id: '2',\n    name: 'قبعة التخرج التقليدية',\n    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n    category: 'cap',\n    price: 79.99,\n    rental_price: 29.99,\n    colors: ['أسود', 'أزرق داكن'],\n    sizes: ['One Size'],\n    images: ['/images/products/cap-traditional-1.jpg'],\n    stock_quantity: 50,\n    is_available: true,\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    rating: 4.6,\n    reviews_count: 28,\n    features: ['مقاس واحد يناسب الجميع', 'شرابة ذهبية', 'تصميم تقليدي'],\n    specifications: {\n      material: 'قطن مخلوط',\n      tassel_color: 'ذهبي',\n      adjustable: 'نعم'\n    }\n  },\n  {\n    id: '3',\n    name: 'وشاح التخرج المطرز',\n    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n    category: 'stole',\n    price: 149.99,\n    rental_price: 49.99,\n    colors: ['أبيض مع ذهبي', 'أزرق مع فضي', 'أحمر مع ذهبي'],\n    sizes: ['One Size'],\n    images: ['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],\n    stock_quantity: 15,\n    is_available: true,\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-25T10:15:00Z',\n    rating: 4.9,\n    reviews_count: 18,\n    features: ['تطريز يدوي', 'خيوط ذهبية', 'تصميم فاخر'],\n    specifications: {\n      material: 'حرير طبيعي',\n      embroidery: 'خيوط ذهبية وفضية',\n      length: '150 سم'\n    }\n  },\n  {\n    id: '4',\n    name: 'شرابة التخرج الذهبية',\n    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n    category: 'tassel',\n    price: 39.99,\n    rental_price: 15.99,\n    colors: ['ذهبي', 'فضي', 'أزرق', 'أحمر'],\n    sizes: ['One Size'],\n    images: ['/images/products/tassel-gold-1.jpg'],\n    stock_quantity: 100,\n    is_available: true,\n    created_at: '2024-01-18T14:00:00Z',\n    updated_at: '2024-01-26T09:30:00Z',\n    rating: 4.7,\n    reviews_count: 35,\n    features: ['خيوط عالية الجودة', 'ألوان ثابتة', 'سهل التركيب'],\n    specifications: {\n      material: 'خيوط حريرية',\n      length: '23 سم',\n      attachment: 'مشبك معدني'\n    }\n  },\n  {\n    id: '5',\n    name: 'قلنسوة الدكتوراه الفاخرة',\n    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n    category: 'hood',\n    price: 199.99,\n    rental_price: 79.99,\n    colors: ['أسود مع ذهبي', 'أزرق مع فضي'],\n    sizes: ['M', 'L', 'XL'],\n    images: ['/images/products/hood-doctorate-1.jpg', '/images/products/hood-doctorate-2.jpg'],\n    stock_quantity: 8,\n    is_available: true,\n    created_at: '2024-01-19T16:00:00Z',\n    updated_at: '2024-01-27T12:00:00Z',\n    rating: 5.0,\n    reviews_count: 12,\n    features: ['تصميم أكاديمي أصيل', 'خامة فاخرة', 'مناسب للدكتوراه'],\n    specifications: {\n      material: 'مخمل عالي الجودة',\n      lining: 'حرير ملون',\n      academic_level: 'دكتوراه'\n    }\n  }\n]\n\n// بيانات وهمية للمدارس\nexport const mockSchools: MockSchool[] = [\n  {\n    id: '1',\n    admin_id: 'admin-school-1',\n    name: 'جامعة الإمارات العربية المتحدة',\n    name_en: 'United Arab Emirates University',\n    name_fr: 'Université des Émirats Arabes Unis',\n    address: 'شارع الجامعة، العين',\n    city: 'العين',\n    phone: '+971-3-713-5000',\n    email: '<EMAIL>',\n    website: 'https://www.uaeu.ac.ae',\n    logo_url: '/images/schools/uaeu-logo.png',\n    graduation_date: '2024-06-15',\n    student_count: 14500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',\n      dress_code: 'formal',\n      photography_allowed: true\n    },\n    created_at: '2024-01-10T08:00:00Z',\n    updated_at: '2024-01-25T10:30:00Z'\n  },\n  {\n    id: '2',\n    admin_id: 'admin-school-2',\n    name: 'الجامعة الأمريكية في الشارقة',\n    name_en: 'American University of Sharjah',\n    name_fr: 'Université Américaine de Sharjah',\n    address: 'شارع الجامعة، الشارقة',\n    city: 'الشارقة',\n    phone: '+971-6-515-5555',\n    email: '<EMAIL>',\n    website: 'https://www.aus.edu',\n    logo_url: '/images/schools/aus-logo.png',\n    graduation_date: '2024-05-20',\n    student_count: 6200,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مسرح الجامعة',\n      dress_code: 'academic',\n      photography_allowed: true\n    },\n    created_at: '2024-01-12T09:15:00Z',\n    updated_at: '2024-01-28T14:20:00Z'\n  },\n  {\n    id: '3',\n    admin_id: 'admin-school-3',\n    name: 'جامعة زايد',\n    name_en: 'Zayed University',\n    name_fr: 'Université Zayed',\n    address: 'شارع الشيخ زايد، دبي',\n    city: 'دبي',\n    phone: '+971-4-402-1111',\n    email: '<EMAIL>',\n    website: 'https://www.zu.ac.ae',\n    logo_url: '/images/schools/zu-logo.png',\n    graduation_date: '2024-06-10',\n    student_count: 9800,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مركز المؤتمرات',\n      dress_code: 'formal',\n      photography_allowed: false\n    },\n    created_at: '2024-01-15T11:00:00Z',\n    updated_at: '2024-02-01T16:45:00Z'\n  },\n  {\n    id: '4',\n    admin_id: 'admin-school-4',\n    name: 'كلية الإمارات للتكنولوجيا',\n    name_en: 'Emirates Institute of Technology',\n    name_fr: 'Institut de Technologie des Émirats',\n    address: 'المنطقة الأكاديمية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-401-4000',\n    email: '<EMAIL>',\n    website: 'https://www.eit.ac.ae',\n    logo_url: '/images/schools/eit-logo.png',\n    graduation_date: '2024-07-05',\n    student_count: 3500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'القاعة الرئيسية',\n      dress_code: 'business',\n      photography_allowed: true\n    },\n    created_at: '2024-01-18T13:30:00Z',\n    updated_at: '2024-02-05T09:15:00Z'\n  },\n  {\n    id: '5',\n    admin_id: 'admin-school-5',\n    name: 'معهد أبوظبي للتعليم التقني',\n    name_en: 'Abu Dhabi Technical Institute',\n    name_fr: 'Institut Technique d\\'Abu Dhabi',\n    address: 'المنطقة الصناعية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-505-2000',\n    email: '<EMAIL>',\n    website: 'https://www.adti.ac.ae',\n    graduation_date: '2024-06-25',\n    student_count: 2800,\n    is_active: false,\n    settings: {\n      graduation_ceremony_location: 'مركز التدريب',\n      dress_code: 'casual',\n      photography_allowed: true\n    },\n    created_at: '2024-01-20T15:45:00Z',\n    updated_at: '2024-02-10T12:00:00Z'\n  }\n]\n\n// بيانات وهمية للطلبات\nexport const mockOrders: MockOrder[] = [\n  {\n    id: '1',\n    order_number: 'GT-240120-001',\n    customer_id: 'student-1',\n    customer_name: 'أحمد محمد علي',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-50-123-4567',\n    status: 'in_production',\n    items: [\n      {\n        id: '1',\n        order_id: '1',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'أسود',\n          size: 'L',\n          embroidery: 'أحمد علي - بكالوريوس هندسة'\n        }\n      },\n      {\n        id: '2',\n        order_id: '1',\n        product_id: '2',\n        product_name: 'قبعة التخرج الأكاديمية',\n        product_image: '/images/products/cap-academic-1.jpg',\n        category: 'cap',\n        quantity: 1,\n        unit_price: 89.99,\n        total_price: 89.99,\n        customizations: {\n          color: 'أسود',\n          size: 'M'\n        }\n      }\n    ],\n    subtotal: 389.98,\n    tax: 19.50,\n    shipping_cost: 25.00,\n    total: 434.48,\n    payment_status: 'paid',\n    payment_method: 'credit_card',\n    shipping_address: {\n      street: 'شارع الجامعة، مبنى 12، شقة 304',\n      city: 'العين',\n      state: 'أبوظبي',\n      postal_code: '17666',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-001-2024',\n    notes: 'يرجى التسليم قبل حفل التخرج',\n    created_at: '2024-01-20T10:30:00Z',\n    updated_at: '2024-01-22T14:15:00Z',\n    delivery_date: '2024-02-15T00:00:00Z',\n    school_id: '1',\n    school_name: 'جامعة الإمارات العربية المتحدة'\n  },\n  {\n    id: '2',\n    order_number: 'GT-**********',\n    customer_id: 'student-2',\n    customer_name: 'فاطمة سالم الزهراني',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-56-789-0123',\n    status: 'delivered',\n    items: [\n      {\n        id: '3',\n        order_id: '2',\n        product_id: '3',\n        product_name: 'ثوب التخرج المميز',\n        product_image: '/images/products/gown-premium-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 399.99,\n        total_price: 399.99,\n        customizations: {\n          color: 'أزرق داكن',\n          size: 'M',\n          embroidery: 'فاطمة الزهراني - ماجستير إدارة أعمال'\n        }\n      }\n    ],\n    subtotal: 399.99,\n    tax: 20.00,\n    shipping_cost: 30.00,\n    total: 449.99,\n    payment_status: 'paid',\n    payment_method: 'bank_transfer',\n    shipping_address: {\n      street: 'شارع الكورنيش، برج الإمارات، الطابق 15',\n      city: 'الشارقة',\n      state: 'الشارقة',\n      postal_code: '27272',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-002-2024',\n    created_at: '2024-01-21T09:15:00Z',\n    updated_at: '2024-01-25T16:30:00Z',\n    delivery_date: '2024-01-28T00:00:00Z',\n    school_id: '2',\n    school_name: 'الجامعة الأمريكية في الشارقة'\n  },\n  {\n    id: '3',\n    order_number: 'GT-**********',\n    customer_id: 'student-3',\n    customer_name: 'خالد عبدالله المنصوري',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-52-456-7890',\n    status: 'pending',\n    items: [\n      {\n        id: '4',\n        order_id: '3',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'بورجوندي',\n          size: 'XL'\n        }\n      },\n      {\n        id: '5',\n        order_id: '3',\n        product_id: '4',\n        product_name: 'وشاح التخرج المطرز',\n        product_image: '/images/products/stole-embroidered-1.jpg',\n        category: 'stole',\n        quantity: 1,\n        unit_price: 149.99,\n        total_price: 149.99,\n        customizations: {\n          color: 'ذهبي',\n          embroidery: 'كلية الهندسة'\n        }\n      }\n    ],\n    subtotal: 449.98,\n    tax: 22.50,\n    shipping_cost: 25.00,\n    total: 497.48,\n    payment_status: 'pending',\n    shipping_address: {\n      street: 'شارع الشيخ زايد، مجمع دبي الأكاديمي',\n      city: 'دبي',\n      state: 'دبي',\n      postal_code: '391186',\n      country: 'الإمارات العربية المتحدة'\n    },\n    created_at: '2024-01-22T14:45:00Z',\n    updated_at: '2024-01-22T14:45:00Z',\n    school_id: '3',\n    school_name: 'جامعة زايد'\n  }\n]\n\n// مساعدات للتعامل مع البيانات الوهمية\nexport class MockDataManager {\n  private static getStorageKey(type: 'pages' | 'menuItems' | 'products' | 'categories' | 'schools' | 'orders'): string {\n    return `mockData_${type}`\n  }\n\n  static getPages(): MockPage[] {\n    if (typeof window === 'undefined') return mockPages\n\n    const stored = localStorage.getItem(this.getStorageKey('pages'))\n    return stored ? JSON.parse(stored) : mockPages\n  }\n\n  static getMenuItems(): MockMenuItem[] {\n    if (typeof window === 'undefined') return mockMenuItems\n\n    const stored = localStorage.getItem(this.getStorageKey('menuItems'))\n    return stored ? JSON.parse(stored) : mockMenuItems\n  }\n\n  static getProducts(): MockProduct[] {\n    if (typeof window === 'undefined') return mockProducts\n\n    const stored = localStorage.getItem(this.getStorageKey('products'))\n    return stored ? JSON.parse(stored) : mockProducts\n  }\n\n  static getCategories(): MockCategory[] {\n    if (typeof window === 'undefined') return mockCategories\n\n    const stored = localStorage.getItem(this.getStorageKey('categories'))\n    return stored ? JSON.parse(stored) : mockCategories\n  }\n\n  static getSchools(): MockSchool[] {\n    if (typeof window === 'undefined') return mockSchools\n\n    const stored = localStorage.getItem(this.getStorageKey('schools'))\n    return stored ? JSON.parse(stored) : mockSchools\n  }\n\n  static getOrders(): MockOrder[] {\n    if (typeof window === 'undefined') return mockOrders\n\n    const stored = localStorage.getItem(this.getStorageKey('orders'))\n    return stored ? JSON.parse(stored) : mockOrders\n  }\n\n  static savePages(pages: MockPage[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('pages'), JSON.stringify(pages))\n    }\n  }\n\n  static saveMenuItems(items: MockMenuItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('menuItems'), JSON.stringify(items))\n    }\n  }\n\n  static saveProducts(products: MockProduct[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('products'), JSON.stringify(products))\n    }\n  }\n\n  static saveCategories(categories: MockCategory[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('categories'), JSON.stringify(categories))\n    }\n  }\n\n  static saveSchools(schools: MockSchool[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('schools'), JSON.stringify(schools))\n    }\n  }\n\n  static saveOrders(orders: MockOrder[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('orders'), JSON.stringify(orders))\n    }\n  }\n\n  static generateId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 11)\n  }\n\n  // مسح جميع البيانات المحفوظة (للاختبار)\n  static clearAllData(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('mockAIModels')\n      localStorage.removeItem('mockModelActivities')\n      localStorage.removeItem('mockPages')\n      localStorage.removeItem('mockPageTemplates')\n      localStorage.removeItem('mockPageProjects')\n      localStorage.removeItem('mockComponentLibrary')\n    }\n  }\n\n  static generateOrderNumber(): string {\n    const date = new Date()\n    const year = date.getFullYear().toString().slice(-2)\n    const month = (date.getMonth() + 1).toString().padStart(2, '0')\n    const day = date.getDate().toString().padStart(2, '0')\n    const orders = this.getOrders()\n    const todayOrders = orders.filter(order =>\n      order.created_at.startsWith(`${date.getFullYear()}-${month}-${day}`)\n    )\n    const orderCount = (todayOrders.length + 1).toString().padStart(3, '0')\n    return `GT-${year}${month}${day}-${orderCount}`\n  }\n\n  // إدارة نماذج الذكاء الاصطناعي\n  static getAIModels(): AIModel[] {\n    if (typeof window === 'undefined') return this.defaultAIModels\n\n    const stored = localStorage.getItem('mockAIModels')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultAIModels\n  }\n\n  static saveAIModels(models: AIModel[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockAIModels', JSON.stringify(models))\n    }\n  }\n\n  static getModelActivities(): ModelActivity[] {\n    if (typeof window === 'undefined') return this.defaultModelActivities\n\n    const stored = localStorage.getItem('mockModelActivities')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultModelActivities\n  }\n\n  static saveModelActivities(activities: ModelActivity[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockModelActivities', JSON.stringify(activities))\n    }\n  }\n\n  // إدارة قوالب الصفحات\n  static getPageTemplates(): PageTemplate[] {\n    if (typeof window === 'undefined') return this.defaultPageTemplates\n\n    const stored = localStorage.getItem('mockPageTemplates')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultPageTemplates\n  }\n\n  static savePageTemplates(templates: PageTemplate[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockPageTemplates', JSON.stringify(templates))\n    }\n  }\n\n  static getPageProjects(): PageProject[] {\n    if (typeof window === 'undefined') return this.defaultPageProjects\n\n    const stored = localStorage.getItem('mockPageProjects')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultPageProjects\n  }\n\n  static savePageProjects(projects: PageProject[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockPageProjects', JSON.stringify(projects))\n    }\n  }\n\n  static getComponentLibrary(): ComponentLibraryItem[] {\n    if (typeof window === 'undefined') return this.defaultComponentLibrary\n\n    const stored = localStorage.getItem('mockComponentLibrary')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultComponentLibrary\n  }\n\n  static saveComponentLibrary(components: ComponentLibraryItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockComponentLibrary', JSON.stringify(components))\n    }\n  }\n\n  // البيانات الافتراضية لنماذج الذكاء الاصطناعي (فارغة للبداية)\n  static defaultAIModels: AIModel[] = []\n\n  static defaultModelActivities: ModelActivity[] = []\n\n  // البيانات الافتراضية لقوالب الصفحات\n  static defaultPageTemplates: PageTemplate[] = [\n    {\n      id: 'template-landing-1',\n      name: 'Landing Page - Modern',\n      nameAr: 'صفحة هبوط - عصرية',\n      nameEn: 'Landing Page - Modern',\n      nameFr: 'Page d\\'atterrissage - Moderne',\n      description: 'قالب صفحة هبوط عصرية مع تصميم جذاب',\n      category: 'landing',\n      components: [\n        {\n          id: 'hero-1',\n          type: 'hero',\n          name: 'Hero Section',\n          props: {\n            content: 'مرحباً بكم في منصة أزياء التخرج',\n            style: { backgroundColor: '#1F2937', color: '#FFFFFF' }\n          },\n          position: { x: 0, y: 0 },\n          size: { width: '100%', height: '500px' },\n          isVisible: true\n        }\n      ],\n      preview: '/images/templates/landing-modern.jpg',\n      thumbnail: '/images/templates/landing-modern-thumb.jpg',\n      isAIGenerated: false,\n      isPremium: false,\n      tags: ['landing', 'modern', 'business'],\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-01T00:00:00Z',\n      usageCount: 45,\n      rating: 4.8,\n      metadata: {\n        colors: ['#1F2937', '#FFFFFF', '#3B82F6'],\n        fonts: ['Inter', 'Cairo'],\n        layout: 'single-page',\n        responsive: true\n      }\n    }\n  ]\n\n  // البيانات الافتراضية لمشاريع الصفحات\n  static defaultPageProjects: PageProject[] = [\n    {\n      id: 'project-1',\n      name: 'موقع أزياء التخرج الرئيسي',\n      description: 'الموقع الرئيسي لعرض منتجات أزياء التخرج',\n      components: [],\n      templateId: 'template-landing-1',\n      generationMode: 'template',\n      settings: {\n        title: 'أزياء التخرج - منصة مغربية متخصصة',\n        description: 'أول منصة مغربية لتأجير وبيع أزياء التخرج',\n        keywords: ['أزياء التخرج', 'تأجير', 'المغرب'],\n        language: 'ar',\n        direction: 'rtl'\n      },\n      isPublished: false,\n      createdAt: '2024-01-15T00:00:00Z',\n      updatedAt: '2024-01-20T10:30:00Z',\n      createdBy: 'admin-1',\n      version: 1\n    }\n  ]\n\n  // البيانات الافتراضية لمكتبة المكونات\n  static defaultComponentLibrary: ComponentLibraryItem[] = [\n    {\n      id: 'comp-hero',\n      name: 'Hero Section',\n      nameAr: 'قسم البطل',\n      type: 'hero',\n      category: 'layout',\n      description: 'قسم رئيسي جذاب في أعلى الصفحة',\n      icon: 'Layout',\n      preview: '/images/components/hero-preview.jpg',\n      defaultProps: {\n        content: 'عنوان رئيسي جذاب',\n        style: { backgroundColor: '#1F2937', color: '#FFFFFF' }\n      },\n      defaultSize: { width: '100%', height: '500px' },\n      isCustom: false,\n      isPremium: false,\n      tags: ['layout', 'header', 'hero'],\n      usageCount: 156\n    },\n    {\n      id: 'comp-button',\n      name: 'Button',\n      nameAr: 'زر',\n      type: 'button',\n      category: 'interactive',\n      description: 'زر تفاعلي قابل للتخصيص',\n      icon: 'MousePointer',\n      preview: '/images/components/button-preview.jpg',\n      defaultProps: {\n        content: 'انقر هنا',\n        style: { backgroundColor: '#3B82F6', color: '#FFFFFF' }\n      },\n      defaultSize: { width: '120px', height: '40px' },\n      isCustom: false,\n      isPremium: false,\n      tags: ['interactive', 'button', 'cta'],\n      usageCount: 234\n    }\n  ]\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;AAoJ1B,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;CACD;AAGM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,iBAAiC;IAC5C;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAa;SAAW;QACzC,OAAO;YAAC;YAAK;YAAK;YAAK;YAAM;SAAM;QACnC,QAAQ;YAAC;YAAuC;SAAsC;QACtF,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAkB;YAAe;SAAoB;QAChE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;SAAY;QAC7B,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAyC;QAClD,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAA0B;YAAe;SAAe;QACnE,gBAAgB;YACd,UAAU;YACV,cAAc;YACd,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;YAAe;SAAe;QACvD,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;YAA4C;SAA2C;QAChG,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAc;YAAc;SAAa;QACpD,gBAAgB;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAO;YAAQ;SAAO;QACvC,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAqC;QAC9C,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAqB;YAAe;SAAc;QAC7D,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;SAAc;QACvC,OAAO;YAAC;YAAK;YAAK;SAAK;QACvB,QAAQ;YAAC;YAAyC;SAAwC;QAC1F,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAsB;YAAc;SAAkB;QACjE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,gBAAgB;QAClB;IACF;CACD;AAGM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,aAAa;IACf;CACD;AAGM,MAAM;IACX,OAAe,cAAc,IAA8E,EAAU;QACnH,OAAO,CAAC,SAAS,EAAE,MAAM;IAC3B;IAEA,OAAO,WAAuB;QAC5B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,eAA+B;QACpC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,cAA6B;QAClC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,gBAAgC;QACrC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,aAA2B;QAChC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,YAAyB;QAC9B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,UAAU,KAAiB,EAAQ;QACxC,uCAAmC;;QAEnC;IACF;IAEA,OAAO,cAAc,KAAqB,EAAQ;QAChD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAa,QAAuB,EAAQ;QACjD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,eAAe,UAA0B,EAAQ;QACtD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,YAAY,OAAqB,EAAQ;QAC9C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,WAAW,MAAmB,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAqB;QAC1B,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACzE;IAEA,wCAAwC;IACxC,OAAO,eAAqB;QAC1B,uCAAmC;;QAOnC;IACF;IAEA,OAAO,sBAA8B;QACnC,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,WAAW,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;QAClD,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAC3D,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAClD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,QAChC,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG,KAAK,WAAW,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;QAErE,MAAM,aAAa,CAAC,YAAY,MAAM,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QACnE,OAAO,CAAC,GAAG,EAAE,OAAO,QAAQ,IAAI,CAAC,EAAE,YAAY;IACjD;IAEA,+BAA+B;IAC/B,OAAO,cAAyB;QAC9B,wCAAmC,OAAO,IAAI,CAAC,eAAe;;QAE9D,MAAM;IAKR;IAEA,OAAO,aAAa,MAAiB,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,qBAAsC;QAC3C,wCAAmC,OAAO,IAAI,CAAC,sBAAsB;;QAErE,MAAM;IAKR;IAEA,OAAO,oBAAoB,UAA2B,EAAQ;QAC5D,uCAAmC;;QAEnC;IACF;IAEA,sBAAsB;IACtB,OAAO,mBAAmC;QACxC,wCAAmC,OAAO,IAAI,CAAC,oBAAoB;;QAEnE,MAAM;IAKR;IAEA,OAAO,kBAAkB,SAAyB,EAAQ;QACxD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,kBAAiC;QACtC,wCAAmC,OAAO,IAAI,CAAC,mBAAmB;;QAElE,MAAM;IAKR;IAEA,OAAO,iBAAiB,QAAuB,EAAQ;QACrD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,sBAA8C;QACnD,wCAAmC,OAAO,IAAI,CAAC,uBAAuB;;QAEtE,MAAM;IAKR;IAEA,OAAO,qBAAqB,UAAkC,EAAQ;QACpE,uCAAmC;;QAEnC;IACF;IAEA,8DAA8D;IAC9D,OAAO,kBAA6B,EAAE,CAAA;IAEtC,OAAO,yBAA0C,EAAE,CAAA;IAEnD,qCAAqC;IACrC,OAAO,uBAAuC;QAC5C;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,UAAU;YACV,YAAY;gBACV;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,OAAO;wBACL,SAAS;wBACT,OAAO;4BAAE,iBAAiB;4BAAW,OAAO;wBAAU;oBACxD;oBACA,UAAU;wBAAE,GAAG;wBAAG,GAAG;oBAAE;oBACvB,MAAM;wBAAE,OAAO;wBAAQ,QAAQ;oBAAQ;oBACvC,WAAW;gBACb;aACD;YACD,SAAS;YACT,WAAW;YACX,eAAe;YACf,WAAW;YACX,MAAM;gBAAC;gBAAW;gBAAU;aAAW;YACvC,WAAW;YACX,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,UAAU;gBACR,QAAQ;oBAAC;oBAAW;oBAAW;iBAAU;gBACzC,OAAO;oBAAC;oBAAS;iBAAQ;gBACzB,QAAQ;gBACR,YAAY;YACd;QACF;KACD,CAAA;IAED,sCAAsC;IACtC,OAAO,sBAAqC;QAC1C;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,YAAY,EAAE;YACd,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBACR,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAC;oBAAgB;oBAAS;iBAAS;gBAC7C,UAAU;gBACV,WAAW;YACb;YACA,aAAa;YACb,WAAW;YACX,WAAW;YACX,WAAW;YACX,SAAS;QACX;KACD,CAAA;IAED,sCAAsC;IACtC,OAAO,0BAAkD;QACvD;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,cAAc;gBACZ,SAAS;gBACT,OAAO;oBAAE,iBAAiB;oBAAW,OAAO;gBAAU;YACxD;YACA,aAAa;gBAAE,OAAO;gBAAQ,QAAQ;YAAQ;YAC9C,UAAU;YACV,WAAW;YACX,MAAM;gBAAC;gBAAU;gBAAU;aAAO;YAClC,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,cAAc;gBACZ,SAAS;gBACT,OAAO;oBAAE,iBAAiB;oBAAW,OAAO;gBAAU;YACxD;YACA,aAAa;gBAAE,OAAO;gBAAS,QAAQ;YAAO;YAC9C,UAAU;YACV,WAAW;YACX,MAAM;gBAAC;gBAAe;gBAAU;aAAM;YACtC,YAAY;QACd;KACD,CAAA;AACH", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/ai-models/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { MockDataManager } from '@/lib/mockData'\nimport { UpdateModelRequest } from '@/types/ai-models'\n\n// GET - جلب نموذج واحد مع تفاصيله\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const includeUsage = searchParams.get('include_usage') === 'true'\n    const includeActivities = searchParams.get('include_activities') === 'true'\n\n    // جلب النموذج\n    const models = MockDataManager.getAIModels()\n    const model = models.find(m => m.id === params.id)\n\n    if (!model) {\n      return NextResponse.json(\n        { error: 'النموذج غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    // إعداد الاستجابة\n    const response: any = { model }\n\n    // إضافة بيانات الاستخدام إذا طُلبت\n    if (includeUsage) {\n      response.usage = model.usage\n      \n      // إحصائيات إضافية\n      response.usageStats = {\n        dailyAverage: model.usage.dailyUsage.length > 0 \n          ? model.usage.dailyUsage.reduce((sum, day) => sum + day.requests, 0) / model.usage.dailyUsage.length\n          : 0,\n        monthlyTotal: model.usage.monthlyUsage.reduce((sum, month) => sum + month.requests, 0),\n        costPerRequest: model.usage.totalRequests > 0 \n          ? model.usage.totalCost / model.usage.totalRequests \n          : 0,\n        tokensPerRequest: model.usage.totalRequests > 0 \n          ? model.usage.totalTokens / model.usage.totalRequests \n          : 0\n      }\n    }\n\n    // إضافة الأنشطة إذا طُلبت\n    if (includeActivities) {\n      const activities = MockDataManager.getModelActivities()\n      response.activities = activities\n        .filter(activity => activity.modelId === params.id)\n        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())\n        .slice(0, 50) // آخر 50 نشاط\n    }\n\n    return NextResponse.json(response)\n\n  } catch (error) {\n    console.error('Error fetching AI model:', error)\n    return NextResponse.json(\n      { error: 'خطأ في جلب النموذج' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT - تحديث نموذج\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const body: UpdateModelRequest = await request.json()\n    const {\n      name,\n      description,\n      apiKey,\n      apiEndpoint,\n      baseUrl,\n      isActive,\n      settings,\n      selectedModels,\n      subModels\n    } = body\n\n    // جلب النماذج الحالية\n    const models = MockDataManager.getAIModels()\n    const modelIndex = models.findIndex(m => m.id === params.id)\n\n    if (modelIndex === -1) {\n      return NextResponse.json(\n        { error: 'النموذج غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    const model = models[modelIndex]\n\n    // التحقق من عدم تكرار الاسم (إذا تم تغييره)\n    if (name && name !== model.name) {\n      const existingModel = models.find(m => \n        m.name.toLowerCase() === name.toLowerCase() && \n        m.provider === model.provider &&\n        m.id !== params.id\n      )\n      if (existingModel) {\n        return NextResponse.json(\n          { error: 'نموذج بنفس الاسم ومقدم الخدمة موجود بالفعل' },\n          { status: 400 }\n        )\n      }\n    }\n\n    // تحديث البيانات\n    const updatedModel = {\n      ...model,\n      ...(name && { name }),\n      ...(description !== undefined && { description }),\n      ...(apiKey !== undefined && { apiKey }),\n      ...(apiEndpoint !== undefined && { apiEndpoint }),\n      ...(baseUrl !== undefined && { baseUrl }),\n      ...(isActive !== undefined && { isActive }),\n      ...(settings && { settings: { ...model.settings, ...settings } }),\n      ...(selectedModels && { selectedModels }),\n      ...(subModels && { subModels }),\n      updatedAt: new Date().toISOString()\n    }\n\n    // تحديث الحالة بناءً على التفعيل\n    if (isActive !== undefined) {\n      updatedModel.status = isActive ? 'active' : 'inactive'\n    }\n\n    // حفظ التحديث\n    models[modelIndex] = updatedModel\n    MockDataManager.saveAIModels(models)\n\n    // إضافة نشاط\n    const activities = MockDataManager.getModelActivities()\n    const changes = []\n    if (name && name !== model.name) changes.push(`الاسم: ${name}`)\n    if (description !== undefined && description !== model.description) changes.push('الوصف')\n    if (apiKey !== undefined) changes.push('مفتاح API')\n    if (apiEndpoint !== undefined) changes.push('نقطة النهاية')\n    if (baseUrl !== undefined) changes.push('Base URL')\n    if (isActive !== undefined && isActive !== model.isActive) {\n      changes.push(isActive ? 'تفعيل' : 'إلغاء تفعيل')\n    }\n    if (settings) changes.push('الإعدادات')\n    if (selectedModels) changes.push(`النماذج المحددة: ${selectedModels.length}`)\n    if (subModels) changes.push(`النماذج الفرعية: ${subModels.length}`)\n\n    activities.push({\n      id: MockDataManager.generateId(),\n      modelId: params.id,\n      type: 'config_change',\n      description: `تم تحديث النموذج: ${changes.join(', ')}`,\n      timestamp: new Date().toISOString(),\n      success: true\n    })\n    MockDataManager.saveModelActivities(activities)\n\n    return NextResponse.json({ \n      message: 'تم تحديث النموذج بنجاح',\n      model: updatedModel \n    })\n\n  } catch (error) {\n    console.error('Error updating AI model:', error)\n    return NextResponse.json(\n      { error: 'خطأ في تحديث النموذج' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - حذف نموذج\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    // جلب النماذج الحالية\n    const models = MockDataManager.getAIModels()\n    const modelIndex = models.findIndex(m => m.id === params.id)\n\n    if (modelIndex === -1) {\n      return NextResponse.json(\n        { error: 'النموذج غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    const model = models[modelIndex]\n\n    // التحقق من وجود استخدام حديث\n    const hasRecentUsage = model.usage.totalRequests > 0 && \n      model.usage.lastUsed && \n      new Date(model.usage.lastUsed).getTime() > Date.now() - (7 * 24 * 60 * 60 * 1000) // آخر 7 أيام\n\n    if (hasRecentUsage) {\n      return NextResponse.json(\n        { \n          error: 'لا يمكن حذف النموذج لوجود استخدام حديث. يرجى إلغاء تفعيله بدلاً من ذلك.',\n          suggestion: 'deactivate'\n        },\n        { status: 400 }\n      )\n    }\n\n    // حذف النموذج\n    models.splice(modelIndex, 1)\n    MockDataManager.saveAIModels(models)\n\n    // إضافة نشاط الحذف\n    const activities = MockDataManager.getModelActivities()\n    activities.push({\n      id: MockDataManager.generateId(),\n      modelId: params.id,\n      type: 'config_change',\n      description: `تم حذف النموذج: ${model.name}`,\n      timestamp: new Date().toISOString(),\n      success: true\n    })\n    MockDataManager.saveModelActivities(activities)\n\n    return NextResponse.json({ \n      message: 'تم حذف النموذج بنجاح',\n      deletedModel: {\n        id: model.id,\n        name: model.name,\n        provider: model.provider\n      }\n    })\n\n  } catch (error) {\n    console.error('Error deleting AI model:', error)\n    return NextResponse.json(\n      { error: 'خطأ في حذف النموذج' },\n      { status: 500 }\n    )\n  }\n}\n\n// PATCH - إجراءات خاصة على النموذج\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const body = await request.json()\n    const { action, data } = body\n\n    if (!action) {\n      return NextResponse.json(\n        { error: 'الإجراء مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    const models = MockDataManager.getAIModels()\n    const modelIndex = models.findIndex(m => m.id === params.id)\n\n    if (modelIndex === -1) {\n      return NextResponse.json(\n        { error: 'النموذج غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    const model = models[modelIndex]\n    const activities = MockDataManager.getModelActivities()\n\n    switch (action) {\n      case 'test_connection':\n        // محاكاة اختبار الاتصال\n        const testSuccess = Math.random() > 0.1 // 90% نجاح\n        const responseTime = Math.floor(Math.random() * 3000) + 500 // 500-3500ms\n        \n        model.lastTestedAt = new Date().toISOString()\n        model.testResult = {\n          success: testSuccess,\n          responseTime,\n          error: testSuccess ? undefined : 'فشل في الاتصال بالخدمة'\n        }\n        \n        if (testSuccess) {\n          model.status = 'active'\n        } else {\n          model.status = 'error'\n        }\n\n        activities.push({\n          id: MockDataManager.generateId(),\n          modelId: params.id,\n          type: 'test',\n          description: `اختبار الاتصال: ${testSuccess ? 'نجح' : 'فشل'}`,\n          timestamp: new Date().toISOString(),\n          duration: responseTime,\n          success: testSuccess,\n          errorMessage: testSuccess ? undefined : 'فشل في الاتصال'\n        })\n        break\n\n      case 'reset_usage':\n        // إعادة تعيين إحصائيات الاستخدام\n        model.usage = {\n          totalRequests: 0,\n          totalTokens: 0,\n          totalCost: 0,\n          dailyUsage: [],\n          monthlyUsage: [],\n          averageResponseTime: 0,\n          successRate: 0\n        }\n\n        activities.push({\n          id: MockDataManager.generateId(),\n          modelId: params.id,\n          type: 'config_change',\n          description: 'تم إعادة تعيين إحصائيات الاستخدام',\n          timestamp: new Date().toISOString(),\n          success: true\n        })\n        break\n\n      case 'add_submodel':\n        // إضافة نموذج فرعي\n        if (!data || !data.name) {\n          return NextResponse.json(\n            { error: 'بيانات النموذج الفرعي مطلوبة' },\n            { status: 400 }\n          )\n        }\n\n        const newSubModel = {\n          id: MockDataManager.generateId(),\n          name: data.name,\n          modelId: params.id,\n          description: data.description || '',\n          version: data.version || '1.0',\n          capabilities: data.capabilities || [],\n          pricing: data.pricing || { inputTokens: 0, outputTokens: 0, currency: 'USD', unit: '1K tokens' },\n          limits: data.limits || { maxTokens: 4096, requestsPerMinute: 60, requestsPerDay: 1000, contextWindow: 4096 },\n          isActive: true,\n          isDefault: false,\n          tags: data.tags || [],\n          releaseDate: new Date().toISOString()\n        }\n\n        model.subModels.push(newSubModel)\n\n        activities.push({\n          id: MockDataManager.generateId(),\n          modelId: params.id,\n          type: 'config_change',\n          description: `تم إضافة نموذج فرعي: ${data.name}`,\n          timestamp: new Date().toISOString(),\n          success: true\n        })\n        break\n\n      default:\n        return NextResponse.json(\n          { error: 'إجراء غير مدعوم' },\n          { status: 400 }\n        )\n    }\n\n    // حفظ التغييرات\n    model.updatedAt = new Date().toISOString()\n    models[modelIndex] = model\n    MockDataManager.saveAIModels(models)\n    MockDataManager.saveModelActivities(activities)\n\n    return NextResponse.json({\n      message: 'تم تنفيذ الإجراء بنجاح',\n      model,\n      action\n    })\n\n  } catch (error) {\n    console.error('Error executing model action:', error)\n    return NextResponse.json(\n      { error: 'خطأ في تنفيذ الإجراء' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAIO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,eAAe,aAAa,GAAG,CAAC,qBAAqB;QAC3D,MAAM,oBAAoB,aAAa,GAAG,CAAC,0BAA0B;QAErE,cAAc;QACd,MAAM,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,WAAgB;YAAE;QAAM;QAE9B,mCAAmC;QACnC,IAAI,cAAc;YAChB,SAAS,KAAK,GAAG,MAAM,KAAK;YAE5B,kBAAkB;YAClB,SAAS,UAAU,GAAG;gBACpB,cAAc,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,IAC1C,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,QAAQ,EAAE,KAAK,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,GAClG;gBACJ,cAAc,MAAM,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,QAAQ,EAAE;gBACpF,gBAAgB,MAAM,KAAK,CAAC,aAAa,GAAG,IACxC,MAAM,KAAK,CAAC,SAAS,GAAG,MAAM,KAAK,CAAC,aAAa,GACjD;gBACJ,kBAAkB,MAAM,KAAK,CAAC,aAAa,GAAG,IAC1C,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC,aAAa,GACnD;YACN;QACF;QAEA,0BAA0B;QAC1B,IAAI,mBAAmB;YACrB,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;YACrD,SAAS,UAAU,GAAG,WACnB,MAAM,CAAC,CAAA,WAAY,SAAS,OAAO,KAAK,OAAO,EAAE,EACjD,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,KAAK,CAAC,GAAG,IAAI,cAAc;;QAChC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqB,GAC9B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,OAA2B,MAAM,QAAQ,IAAI;QACnD,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,MAAM,EACN,WAAW,EACX,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,SAAS,EACV,GAAG;QAEJ,sBAAsB;QACtB,MAAM,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;QAE3D,IAAI,eAAe,CAAC,GAAG;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,MAAM,CAAC,WAAW;QAEhC,4CAA4C;QAC5C,IAAI,QAAQ,SAAS,MAAM,IAAI,EAAE;YAC/B,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAChC,EAAE,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW,MACzC,EAAE,QAAQ,KAAK,MAAM,QAAQ,IAC7B,EAAE,EAAE,KAAK,OAAO,EAAE;YAEpB,IAAI,eAAe;gBACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA6C,GACtD;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,iBAAiB;QACjB,MAAM,eAAe;YACnB,GAAG,KAAK;YACR,GAAI,QAAQ;gBAAE;YAAK,CAAC;YACpB,GAAI,gBAAgB,aAAa;gBAAE;YAAY,CAAC;YAChD,GAAI,WAAW,aAAa;gBAAE;YAAO,CAAC;YACtC,GAAI,gBAAgB,aAAa;gBAAE;YAAY,CAAC;YAChD,GAAI,YAAY,aAAa;gBAAE;YAAQ,CAAC;YACxC,GAAI,aAAa,aAAa;gBAAE;YAAS,CAAC;YAC1C,GAAI,YAAY;gBAAE,UAAU;oBAAE,GAAG,MAAM,QAAQ;oBAAE,GAAG,QAAQ;gBAAC;YAAE,CAAC;YAChE,GAAI,kBAAkB;gBAAE;YAAe,CAAC;YACxC,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iCAAiC;QACjC,IAAI,aAAa,WAAW;YAC1B,aAAa,MAAM,GAAG,WAAW,WAAW;QAC9C;QAEA,cAAc;QACd,MAAM,CAAC,WAAW,GAAG;QACrB,wHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAE7B,aAAa;QACb,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;QACrD,MAAM,UAAU,EAAE;QAClB,IAAI,QAAQ,SAAS,MAAM,IAAI,EAAE,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM;QAC9D,IAAI,gBAAgB,aAAa,gBAAgB,MAAM,WAAW,EAAE,QAAQ,IAAI,CAAC;QACjF,IAAI,WAAW,WAAW,QAAQ,IAAI,CAAC;QACvC,IAAI,gBAAgB,WAAW,QAAQ,IAAI,CAAC;QAC5C,IAAI,YAAY,WAAW,QAAQ,IAAI,CAAC;QACxC,IAAI,aAAa,aAAa,aAAa,MAAM,QAAQ,EAAE;YACzD,QAAQ,IAAI,CAAC,WAAW,UAAU;QACpC;QACA,IAAI,UAAU,QAAQ,IAAI,CAAC;QAC3B,IAAI,gBAAgB,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,eAAe,MAAM,EAAE;QAC5E,IAAI,WAAW,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,UAAU,MAAM,EAAE;QAElE,WAAW,IAAI,CAAC;YACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B,SAAS,OAAO,EAAE;YAClB,MAAM;YACN,aAAa,CAAC,kBAAkB,EAAE,QAAQ,IAAI,CAAC,OAAO;YACtD,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;QACX;QACA,wHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,sBAAsB;QACtB,MAAM,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;QAE3D,IAAI,eAAe,CAAC,GAAG;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,MAAM,CAAC,WAAW;QAEhC,8BAA8B;QAC9B,MAAM,iBAAiB,MAAM,KAAK,CAAC,aAAa,GAAG,KACjD,MAAM,KAAK,CAAC,QAAQ,IACpB,IAAI,KAAK,MAAM,KAAK,CAAC,QAAQ,EAAE,OAAO,KAAK,KAAK,GAAG,KAAM,IAAI,KAAK,KAAK,KAAK,KAAM,aAAa;;QAEjG,IAAI,gBAAgB;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,YAAY;YACd,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,OAAO,MAAM,CAAC,YAAY;QAC1B,wHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAE7B,mBAAmB;QACnB,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;QACrD,WAAW,IAAI,CAAC;YACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B,SAAS,OAAO,EAAE;YAClB,MAAM;YACN,aAAa,CAAC,gBAAgB,EAAE,MAAM,IAAI,EAAE;YAC5C,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;QACX;QACA,wHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,cAAc;gBACZ,IAAI,MAAM,EAAE;gBACZ,MAAM,MAAM,IAAI;gBAChB,UAAU,MAAM,QAAQ;YAC1B;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqB,GAC9B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgB,GACzB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;QAE3D,IAAI,eAAe,CAAC,GAAG;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,MAAM,CAAC,WAAW;QAChC,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;QAErD,OAAQ;YACN,KAAK;gBACH,wBAAwB;gBACxB,MAAM,cAAc,KAAK,MAAM,KAAK,IAAI,WAAW;;gBACnD,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,IAAI,aAAa;;gBAEzE,MAAM,YAAY,GAAG,IAAI,OAAO,WAAW;gBAC3C,MAAM,UAAU,GAAG;oBACjB,SAAS;oBACT;oBACA,OAAO,cAAc,YAAY;gBACnC;gBAEA,IAAI,aAAa;oBACf,MAAM,MAAM,GAAG;gBACjB,OAAO;oBACL,MAAM,MAAM,GAAG;gBACjB;gBAEA,WAAW,IAAI,CAAC;oBACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;oBAC9B,SAAS,OAAO,EAAE;oBAClB,MAAM;oBACN,aAAa,CAAC,gBAAgB,EAAE,cAAc,QAAQ,OAAO;oBAC7D,WAAW,IAAI,OAAO,WAAW;oBACjC,UAAU;oBACV,SAAS;oBACT,cAAc,cAAc,YAAY;gBAC1C;gBACA;YAEF,KAAK;gBACH,iCAAiC;gBACjC,MAAM,KAAK,GAAG;oBACZ,eAAe;oBACf,aAAa;oBACb,WAAW;oBACX,YAAY,EAAE;oBACd,cAAc,EAAE;oBAChB,qBAAqB;oBACrB,aAAa;gBACf;gBAEA,WAAW,IAAI,CAAC;oBACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;oBAC9B,SAAS,OAAO,EAAE;oBAClB,MAAM;oBACN,aAAa;oBACb,WAAW,IAAI,OAAO,WAAW;oBACjC,SAAS;gBACX;gBACA;YAEF,KAAK;gBACH,mBAAmB;gBACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;oBACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBAAE,OAAO;oBAA+B,GACxC;wBAAE,QAAQ;oBAAI;gBAElB;gBAEA,MAAM,cAAc;oBAClB,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;oBAC9B,MAAM,KAAK,IAAI;oBACf,SAAS,OAAO,EAAE;oBAClB,aAAa,KAAK,WAAW,IAAI;oBACjC,SAAS,KAAK,OAAO,IAAI;oBACzB,cAAc,KAAK,YAAY,IAAI,EAAE;oBACrC,SAAS,KAAK,OAAO,IAAI;wBAAE,aAAa;wBAAG,cAAc;wBAAG,UAAU;wBAAO,MAAM;oBAAY;oBAC/F,QAAQ,KAAK,MAAM,IAAI;wBAAE,WAAW;wBAAM,mBAAmB;wBAAI,gBAAgB;wBAAM,eAAe;oBAAK;oBAC3G,UAAU;oBACV,WAAW;oBACX,MAAM,KAAK,IAAI,IAAI,EAAE;oBACrB,aAAa,IAAI,OAAO,WAAW;gBACrC;gBAEA,MAAM,SAAS,CAAC,IAAI,CAAC;gBAErB,WAAW,IAAI,CAAC;oBACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;oBAC9B,SAAS,OAAO,EAAE;oBAClB,MAAM;oBACN,aAAa,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;oBAChD,WAAW,IAAI,OAAO,WAAW;oBACjC,SAAS;gBACX;gBACA;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAkB,GAC3B;oBAAE,QAAQ;gBAAI;QAEpB;QAEA,gBAAgB;QAChB,MAAM,SAAS,GAAG,IAAI,OAAO,WAAW;QACxC,MAAM,CAAC,WAAW,GAAG;QACrB,wHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAC7B,wHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}