{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: UserRole\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({\n  children,\n  requiredRole,\n  redirectTo = '/auth'\n}: ProtectedRouteProps) {\n  const { user, profile, loading, hasRole } = useAuth()\n  const router = useRouter()\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    if (mounted && !loading) {\n      if (!user) {\n        router.push(redirectTo)\n        return\n      }\n\n      if (requiredRole && !hasRole(requiredRole)) {\n        router.push('/unauthorized')\n        return\n      }\n    }\n  }, [mounted, user, profile, loading, requiredRole, hasRole, router, redirectTo])\n\n  // Show loading until mounted and auth is resolved\n  if (!mounted || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-blue-600 mb-4\" />\n            <p className=\"text-gray-600 dark:text-gray-300\">جاري التحميل...</p>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect\n  }\n\n  if (requiredRole && !hasRole(requiredRole)) {\n    return null // Will redirect\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAeO,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EACZ,aAAa,OAAO,EACA;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,CAAC,SAAS;YACvB,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;gBAC1C,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAS;QAAS;QAAc;QAAS;QAAQ;KAAW;IAE/E,kDAAkD;IAClD,IAAI,CAAC,WAAW,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;QAC1C,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,kHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,eAAe;YAAC;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,cAAc;YAC3D,UAAU;QACZ;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,8OAAC,4IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,kHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,8OAAC,4IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAW,kHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case UserRole.ADMIN:\n        return '/dashboard/admin'\n      case UserRole.SCHOOL:\n        return '/dashboard/school'\n      case UserRole.DELIVERY:\n        return '/dashboard/delivery'\n      case UserRole.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAhBA;;;;;;;;;AAkBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,8OAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,8OAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;AAuBO,SAAS;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,8OAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,8OAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;wEAAI,WAAU;;;;;;kFAEjB,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,8OAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;AAeO,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,8OAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,8OAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;;gCACZ,0BACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;AAGO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC", "debugId": null}}, {"offset": {"line": 1710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [cartItemsCount, setCartItemsCount] = useState(0)\n  const [wishlistCount, setWishlistCount] = useState(0)\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n  // Mock cart and wishlist counts - replace with actual data\n  useEffect(() => {\n    // Simulate getting cart items from localStorage or API\n    const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]')\n    setCartItemsCount(cartItems.length)\n\n    const wishlistItems = JSON.parse(localStorage.getItem('wishlistItems') || '[]')\n    setWishlistCount(wishlistItems.length)\n  }, [])\n\n  // جلب عناصر القائمة من قاعدة البيانات\n  useEffect(() => {\n    const fetchMenuItems = async () => {\n      try {\n        setLoading(true)\n        // جلب جميع عناصر القائمة (الرئيسية والفرعية)\n        const response = await fetch('/api/menu-items')\n\n        if (response.ok) {\n          const data = await response.json()\n          setMenuItems(data.menuItems || [])\n        } else {\n          // في حالة فشل API، استخدم القائمة الافتراضية\n          console.warn('Failed to fetch menu items from API, using default menu')\n          setMenuItems([])\n        }\n      } catch (error) {\n        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية\n        console.warn('Error fetching menu items, using default menu:', error)\n        setMenuItems([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchMenuItems()\n  }, [])\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = () => {\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems.filter(item => !item.parent_id && item.is_active)\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'Grid3X3':\n            icon = <Grid3X3 className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      // البحث عن القوائم الفرعية لهذا العنصر\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems: NavItem[] = [\n    {\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const navItems = loading ? defaultNavItems : (menuItems.length > 0 ? getNavItemsFromDB() : defaultNavItems)\n\n  // استخدام عناصر القائمة فقط (بدون لوحة التحكم في القائمة الرئيسية)\n  const { user, profile } = useAuth()\n  const allNavItems = navItems\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center gap-1\">\n            {allNavItems.map((item) => {\n              // تحديد ما إذا كان الرابط خارجي\n              const isExternal = item.target_type === 'external'\n              const hasSubItems = item.subItems && item.subItems.length > 0\n\n              // إذا كان العنصر له قوائم فرعية\n              if (hasSubItems) {\n                return (\n                  <div key={item.href} className=\"relative group\">\n                    <button\n                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                      }`}\n                    >\n                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                        {item.icon}\n                      </span>\n                      <span className=\"text-sm font-medium\">\n                        {item.label}\n                      </span>\n                      <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                    </button>\n\n                    {/* القائمة الفرعية */}\n                    <div className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-2\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  </div>\n                )\n              }\n\n              // العناصر العادية بدون قوائم فرعية\n              const linkProps = isExternal\n                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                : { href: item.href }\n\n              return (\n                <Link\n                  key={item.href}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartItemsCount > 99 ? '99+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n          </div>\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartItemsCount > 9 ? '9+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            <nav className=\"flex flex-col gap-1 mb-6\">\n              {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.href} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              onClick={() => setIsMobileMenuOpen(false)}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.href}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n            </nav>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;;;;;AAyDO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;IACtB,GAAG;QAAC;KAAS;IAEb,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uDAAuD;QACvD,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,kBAAkB,UAAU,MAAM;QAElC,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QAC1E,iBAAiB,cAAc,MAAM;IACvC,GAAG,EAAE;IAEL,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,WAAW;gBACX,6CAA6C;gBAC7C,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,aAAa,KAAK,SAAS,IAAI,EAAE;gBACnC,OAAO;oBACL,6CAA6C;oBAC7C,QAAQ,IAAI,CAAC;oBACb,aAAa,EAAE;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,mDAAmD;gBACnD,QAAQ,IAAI,CAAC,kDAAkD;gBAC/D,aAAa,EAAE;YACjB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB;QACxB,+DAA+D;QAC/D,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS;QAE5E,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,8OAAC,mMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF;wBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,uCAAuC;YACvC,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS,EACpE,GAAG,CAAC,CAAA;gBACH,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBACvC,WAAW,QAAQ,QAAQ;gBAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBAC9C,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,IAAI,UAAU,QAAQ,YAAY;gBAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;oBAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC5C;gBAEA,OAAO;oBACL,MAAM;oBACN,OAAO;oBACP,aAAa,QAAQ,WAAW;gBAClC;YACF;YAEF,OAAO;gBACL;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;gBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAC7C;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,WAAW,UAAU,kBAAmB,UAAU,MAAM,GAAG,IAAI,sBAAsB;IAE3F,mEAAmE;IACnE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc;IAEpB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,8OAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,gCAAgC;gCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;gCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;gCAE5D,gCAAgC;gCAChC,IAAI,aAAa;oCACf,qBACE,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;kEAEF,8OAAC;wDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;kEAChH,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;kEAEb,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAIzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DANrB,QAAQ,IAAI;;;;;oDAUvB;;;;;;;;;;;;uCAtCI,KAAK,IAAI;;;;;gCA2CvB;gCAEA,mCAAmC;gCACnC,MAAM,YAAY,aACd;oCAAE,MAAM,KAAK,IAAI;oCAAE,QAAQ;oCAAU,KAAK;gCAAsB,IAChE;oCAAE,MAAM,KAAK,IAAI;gCAAC;gCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEF,GAAG,SAAS;oCACb,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;sDAEF,8OAAC;4CAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sDAChH,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;wCAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,SAAS,KAAK,IAAI,mBACjB,8OAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOvC,8OAAC,2JAAA,CAAA,uBAAoB;;;;;8CAGrB,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;sDACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;sCAIX,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOrC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,8OAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,8OAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,IAAI;;;;;oDAWvB;;;;;;;2CA7CM,KAAK,IAAI;;;;;oCAiDvB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,8OAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,IAAI;;;;;gCA2BpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;0DACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;uCAEe", "debugId": null}}, {"offset": {"line": 2684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Footer.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from 'next/link'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { \n  GraduationCap, \n  Mail, \n  Phone, \n  MapPin,\n  Facebook,\n  Twitter,\n  Instagram,\n  Linkedin,\n  Heart\n} from 'lucide-react'\n\nexport function Footer() {\n  const { t } = useTranslation()\n\n  const currentYear = new Date().getFullYear()\n\n  const footerLinks = {\n    company: [\n      { href: '/about', label: 'من نحن' },\n      { href: '/contact', label: 'تواصل معنا' },\n      { href: '/support', label: 'الدعم الفني' },\n      { href: '/privacy', label: 'سياسة الخصوصية' }\n    ],\n    services: [\n      { href: '/catalog', label: 'الكتالوج' },\n      { href: '/customize', label: 'التخصيص' },\n      { href: '/track-order', label: 'تتبع الطلب' },\n      { href: '/size-guide', label: 'دليل المقاسات' }\n    ],\n    support: [\n      { href: '/faq', label: 'الأسئلة الشائعة' },\n      { href: '/shipping', label: 'الشحن والتوصيل' },\n      { href: '/returns', label: 'الإرجاع والاستبدال' },\n      { href: '/terms', label: 'الشروط والأحكام' }\n    ]\n  }\n\n  const socialLinks = [\n    { href: '#', icon: Facebook, label: 'Facebook' },\n    { href: '#', icon: Twitter, label: 'Twitter' },\n    { href: '#', icon: Instagram, label: 'Instagram' },\n    { href: '#', icon: Linkedin, label: 'LinkedIn' }\n  ]\n\n  return (\n    <footer className=\"bg-gray-900 text-white mt-16\">\n      <div className=\"container mx-auto px-4 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2\">\n              <GraduationCap className=\"h-8 w-8 text-blue-400\" />\n              <span className=\"text-xl font-bold\">Graduation Toqs</span>\n            </div>\n            <p className=\"text-gray-300 arabic-text leading-relaxed\">\n              أول منصة مغربية متخصصة في تأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي\n            </p>\n            \n            {/* Contact Info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2 text-sm\">\n                <Mail className=\"h-4 w-4 text-blue-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm\">\n                <Phone className=\"h-4 w-4 text-blue-400\" />\n                <span>+212 6 12 34 56 78</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm\">\n                <MapPin className=\"h-4 w-4 text-blue-400\" />\n                <span className=\"arabic-text\">بني ملال، المغرب</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الشركة</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الخدمات</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.services.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الدعم</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Social Media & Copyright */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            {/* Social Links */}\n            <div className=\"flex items-center gap-4\">\n              <span className=\"text-gray-400 arabic-text\">تابعنا على:</span>\n              {socialLinks.map((social) => {\n                const Icon = social.icon\n                return (\n                  <a\n                    key={social.label}\n                    href={social.href}\n                    className=\"text-gray-400 hover:text-blue-400 transition-colors\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </a>\n                )\n              })}\n            </div>\n\n            {/* Copyright */}\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-gray-400 text-sm arabic-text\">\n                © {currentYear} Graduation Toqs. جميع الحقوق محفوظة\n              </p>\n              <p className=\"text-gray-500 text-xs mt-1 flex items-center justify-center md:justify-end gap-1\">\n                <span className=\"arabic-text\">صُنع بـ</span>\n                <Heart className=\"h-3 w-3 text-red-500\" />\n                <span className=\"arabic-text\">في المغرب</span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAU,OAAO;YAAS;YAClC;gBAAE,MAAM;gBAAY,OAAO;YAAa;YACxC;gBAAE,MAAM;gBAAY,OAAO;YAAc;YACzC;gBAAE,MAAM;gBAAY,OAAO;YAAiB;SAC7C;QACD,UAAU;YACR;gBAAE,MAAM;gBAAY,OAAO;YAAW;YACtC;gBAAE,MAAM;gBAAc,OAAO;YAAU;YACvC;gBAAE,MAAM;gBAAgB,OAAO;YAAa;YAC5C;gBAAE,MAAM;gBAAe,OAAO;YAAgB;SAC/C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAQ,OAAO;YAAkB;YACzC;gBAAE,MAAM;gBAAa,OAAO;YAAiB;YAC7C;gBAAE,MAAM;gBAAY,OAAO;YAAqB;YAChD;gBAAE,MAAM;gBAAU,OAAO;YAAkB;SAC5C;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAK,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;QAC/C;YAAE,MAAM;YAAK,MAAM,wMAAA,CAAA,UAAO;YAAE,OAAO;QAAU;QAC7C;YAAE,MAAM;YAAK,MAAM,4MAAA,CAAA,YAAS;YAAE,OAAO;QAAY;QACjD;YAAE,MAAM;YAAK,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;KAChD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA4C;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;sCAMpC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAG,WAAU;8CACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;oCAC3C,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,8OAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,cAAY,OAAO,KAAK;sDAExB,cAAA,8OAAC;gDAAK,WAAU;;;;;;2CALX,OAAO,KAAK;;;;;oCAQvB;;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CAAoC;4CAC5C;4CAAY;;;;;;;kDAEjB,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAc;;;;;;0DAC9B,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C", "debugId": null}}, {"offset": {"line": 3156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/layouts/PageLayout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { ReactNode } from 'react'\nimport { Navigation } from '@/components/Navigation'\nimport { Footer } from '@/components/Footer'\n\ninterface PageLayoutProps {\n  children: ReactNode\n  className?: string\n  showFooter?: boolean\n  containerClassName?: string\n}\n\nexport function PageLayout({ \n  children, \n  className = \"\", \n  showFooter = true,\n  containerClassName = \"container mx-auto px-4 py-8\"\n}: PageLayoutProps) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      {/* Header Navigation - موحد عبر جميع الصفحات */}\n      <Navigation />\n      \n      {/* Main Content */}\n      <main className={containerClassName}>\n        {children}\n      </main>\n      \n      {/* Footer - اختياري */}\n      {showFooter && <Footer />}\n    </div>\n  )\n}\n\n// Layout خاص بلوحات التحكم\nexport function DashboardLayout({ \n  children, \n  className = \"\",\n  title,\n  description \n}: PageLayoutProps & { title?: string; description?: string }) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      <Navigation />\n      \n      <main className=\"container mx-auto px-4 py-8\">\n        {(title || description) && (\n          <div className=\"mb-8\">\n            {title && (\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text mb-2\">\n                {title}\n              </h1>\n            )}\n            {description && (\n              <p className=\"text-gray-600 dark:text-gray-300 arabic-text\">\n                {description}\n              </p>\n            )}\n          </div>\n        )}\n        {children}\n      </main>\n    </div>\n  )\n}\n\n// Layout للصفحات الخاصة (مثل 404، خطأ)\nexport function ErrorLayout({ children, className = \"\" }: PageLayoutProps) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      <Navigation />\n      <main className=\"container mx-auto px-4 py-8 flex items-center justify-center min-h-[calc(100vh-200px)]\">\n        {children}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAJA;;;;AAaO,SAAS,WAAW,EACzB,QAAQ,EACR,YAAY,EAAE,EACd,aAAa,IAAI,EACjB,qBAAqB,6BAA6B,EAClC;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAC,yHAAyH,EAAE,WAAW;;0BAErJ,8OAAC,gIAAA,CAAA,aAAU;;;;;0BAGX,8OAAC;gBAAK,WAAW;0BACd;;;;;;YAIF,4BAAc,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAG5B;AAGO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EAAE,EACd,KAAK,EACL,WAAW,EACgD;IAC3D,qBACE,8OAAC;QAAI,WAAW,CAAC,yHAAyH,EAAE,WAAW;;0BACrJ,8OAAC,gIAAA,CAAA,aAAU;;;;;0BAEX,8OAAC;gBAAK,WAAU;;oBACb,CAAC,SAAS,WAAW,mBACpB,8OAAC;wBAAI,WAAU;;4BACZ,uBACC,8OAAC;gCAAG,WAAU;0CACX;;;;;;4BAGJ,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;oBAKR;;;;;;;;;;;;;AAIT;AAGO,SAAS,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;IACvE,qBACE,8OAAC;QAAI,WAAW,CAAC,wHAAwH,EAAE,WAAW;;0BACpJ,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 3278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/ai-models/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { DashboardLayout } from '@/components/layouts/PageLayout'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Textarea } from '@/components/ui/textarea'\nimport { toast } from 'sonner'\nimport {\n  Brain,\n  Plus,\n  Activity,\n  ArrowLeft,\n  Home,\n  Settings,\n  Zap\n} from 'lucide-react'\n\nexport default function AIModelsPage() {\n  const { user, profile } = useAuth()\n  const [settingsOpen, setSettingsOpen] = useState(false)\n  const [addModelOpen, setAddModelOpen] = useState(false)\n\n  const handleTest = () => {\n    toast.success('تم اختبار الصفحة بنجاح!')\n  }\n\n  const handleSettings = () => {\n    setSettingsOpen(true)\n  }\n\n  const handleAddModel = () => {\n    setAddModelOpen(true)\n  }\n\n  const handleHealthCheck = () => {\n    toast.loading('جاري فحص صحة النماذج...', {\n      description: 'يرجى الانتظار'\n    })\n\n    // محاكاة فحص الصحة\n    setTimeout(() => {\n      toast.success('تم فحص جميع النماذج بنجاح!', {\n        description: '✅ 5 نماذج تعمل، ⚠️ 2 تحذيرات، ❌ 0 لا تعمل'\n      })\n    }, 2000)\n  }\n\n  return (\n    <ProtectedRoute requiredRole={UserRole.ADMIN}>\n      <DashboardLayout\n        title=\"إدارة مقدمي خدمات الذكاء الاصطناعي\"\n        description=\"إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم\"\n      >\n        {/* شريط التنقل العلوي */}\n        <div className=\"flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border\">\n          <div className=\"flex items-center gap-4\">\n            <Link\n              href=\"/dashboard/admin\"\n              className=\"flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\"\n            >\n              <ArrowLeft className=\"h-4 w-4\" />\n              <span className=\"arabic-text\">العودة للوحة التحكم</span>\n            </Link>\n            <div className=\"h-4 w-px bg-gray-300 dark:bg-gray-600\"></div>\n            <Link\n              href=\"/\"\n              className=\"flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors\"\n            >\n              <Home className=\"h-4 w-4\" />\n              <span className=\"arabic-text\">الصفحة الرئيسية</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <Brain className=\"h-6 w-6 text-blue-600\" />\n            <span className=\"font-semibold text-gray-900 dark:text-white arabic-text\">نماذج الذكاء الاصطناعي</span>\n          </div>\n        </div>\n\n        {/* المحتوى الرئيسي */}\n        <div className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text flex items-center gap-2\">\n                <Brain className=\"h-5 w-5\" />\n                إدارة نماذج الذكاء الاصطناعي\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <p className=\"text-gray-600 dark:text-gray-400 arabic-text\">\n                  تم تحديث الصفحة بنجاح مع إضافة القائمة الرئيسية وروابط التنقل المحسنة.\n                </p>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                  <div className=\"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-sm font-medium arabic-text\">القائمة الرئيسية</span>\n                    </div>\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 arabic-text\">موحدة عبر المنصة</p>\n                  </div>\n                  \n                  <div className=\"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-sm font-medium arabic-text\">روابط التنقل</span>\n                    </div>\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 arabic-text\">سهولة الوصول</p>\n                  </div>\n                  \n                  <div className=\"p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-sm font-medium arabic-text\">تصميم متجاوب</span>\n                    </div>\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 arabic-text\">جميع الأجهزة</p>\n                  </div>\n                  \n                  <div className=\"p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-sm font-medium arabic-text\">الوضع الليلي</span>\n                    </div>\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 arabic-text\">دعم كامل</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex flex-wrap gap-2 pt-4\">\n                  <Button onClick={handleAddModel}>\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    إضافة نموذج\n                  </Button>\n                  <Button variant=\"outline\" onClick={handleSettings}>\n                    <Settings className=\"h-4 w-4 mr-2\" />\n                    إعدادات النماذج\n                  </Button>\n                  <Button variant=\"outline\" onClick={handleHealthCheck}>\n                    <Activity className=\"h-4 w-4 mr-2\" />\n                    فحص الصحة\n                  </Button>\n                  <Button variant=\"outline\" onClick={handleTest}>\n                    <Zap className=\"h-4 w-4 mr-2\" />\n                    اختبار الصفحة\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n\n        </div>\n\n        {/* Dialog إعدادات النماذج */}\n        <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>\n          <DialogContent className=\"max-w-md\">\n            <DialogHeader>\n              <DialogTitle className=\"arabic-text flex items-center gap-2\">\n                <Settings className=\"h-5 w-5\" />\n                إعدادات النماذج\n              </DialogTitle>\n              <DialogDescription className=\"arabic-text\">\n                تكوين وإدارة إعدادات نماذج الذكاء الاصطناعي\n              </DialogDescription>\n            </DialogHeader>\n\n            <div className=\"space-y-4 py-4\">\n              <div className=\"space-y-3\">\n                <h4 className=\"text-sm font-medium arabic-text\">الإعدادات العامة</h4>\n                <div className=\"space-y-2\">\n                  <Button variant=\"outline\" className=\"w-full justify-start arabic-text\">\n                    <Brain className=\"h-4 w-4 mr-2\" />\n                    إدارة مقدمي الخدمة\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full justify-start arabic-text\">\n                    <Activity className=\"h-4 w-4 mr-2\" />\n                    مراقبة الأداء\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full justify-start arabic-text\">\n                    <Zap className=\"h-4 w-4 mr-2\" />\n                    اختبار الاتصال\n                  </Button>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <h4 className=\"text-sm font-medium arabic-text\">إعدادات متقدمة</h4>\n                <div className=\"space-y-2\">\n                  <Button variant=\"outline\" className=\"w-full justify-start arabic-text\">\n                    <Settings className=\"h-4 w-4 mr-2\" />\n                    تكوين API Keys\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full justify-start arabic-text\">\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    إضافة مقدم جديد\n                  </Button>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end gap-2\">\n              <Button variant=\"outline\" onClick={() => setSettingsOpen(false)}>\n                إغلاق\n              </Button>\n              <Button onClick={() => {\n                toast.success('تم حفظ الإعدادات بنجاح!')\n                setSettingsOpen(false)\n              }}>\n                حفظ\n              </Button>\n            </div>\n          </DialogContent>\n        </Dialog>\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAhBA;;;;;;;;;;;;;AA0Be,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa;QACjB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB;QACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,2BAA2B;YACvC,aAAa;QACf;QAEA,mBAAmB;QACnB,WAAW;YACT,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,8BAA8B;gBAC1C,aAAa;YACf;QACF,GAAG;IACL;IAEA,qBACE,8OAAC,4IAAA,CAAA,iBAAc;QAAC,cAAc,oHAAA,CAAA,WAAQ,CAAC,KAAK;kBAC1C,cAAA,8OAAC,2IAAA,CAAA,kBAAe;YACd,OAAM;YACN,aAAY;;8BAGZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;8CAEhC,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAA0D;;;;;;;;;;;;;;;;;;8BAK9E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIjC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAI5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;sEAEpD,8OAAC;4DAAE,WAAU;sEAAuD;;;;;;;;;;;;8DAGtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;sEAEpD,8OAAC;4DAAE,WAAU;sEAAuD;;;;;;;;;;;;8DAGtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;sEAEpD,8OAAC;4DAAE,WAAU;sEAAuD;;;;;;;;;;;;8DAGtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;sEAEpD,8OAAC;4DAAE,WAAU;sEAAuD;;;;;;;;;;;;;;;;;;sDAIxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAY5C,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAc,cAAc;8BACxC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,8OAAC,kIAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAAc;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGpC,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAMtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAOzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,gBAAgB;kDAAQ;;;;;;kDAGjE,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;4CACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4CACd,gBAAgB;wCAClB;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjB", "debugId": null}}]}