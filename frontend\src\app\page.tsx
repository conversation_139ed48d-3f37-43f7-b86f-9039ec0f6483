"use client"

import { <PERSON>rad<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PageLayout } from "@/components/layouts/PageLayout";
import { useTranslation } from "@/hooks/useTranslation";

export default function Home() {
  const { t } = useTranslation();

  return (
    <PageLayout containerClassName="container mx-auto px-4 py-12">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-gray-900 dark:text-white mb-6 arabic-text">
            🎓 {t('home.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto arabic-text leading-relaxed">
            {t('home.subtitle')}
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 arabic-text" asChild>
              <a href="/customize">{t('home.startCustomizing')}</a>
            </Button>
            <Button variant="outline" size="lg" className="arabic-text" asChild>
              <a href="/catalog">{t('home.browseCatalog')}</a>
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <Card className="text-center">
            <CardHeader>
              <Palette className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <CardTitle className="arabic-text">{t('home.features.customization.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="arabic-text">
                {t('home.features.customization.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Sparkles className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
              <CardTitle className="arabic-text">{t('home.features.ai.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="arabic-text">
                {t('home.features.ai.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Users className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <CardTitle className="arabic-text">{t('home.features.roles.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="arabic-text">
                {t('home.features.roles.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <GraduationCap className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <CardTitle className="arabic-text">{t('home.features.tracking.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="arabic-text">
                {t('home.features.tracking.description')}
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Language Support */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            {t('home.languageSupport')}
          </h3>
          <div className="flex justify-center gap-8 text-lg">
            <span className="flex items-center gap-2">
              🇲🇦 العربية
            </span>
            <span className="flex items-center gap-2">
              🇫🇷 Français
            </span>
            <span className="flex items-center gap-2">
              🇬🇧 English
            </span>
          </div>
        </div>
    </PageLayout>
  );
}
