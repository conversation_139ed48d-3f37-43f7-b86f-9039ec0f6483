import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'

// POST - تصدير مشروع كـ HTML
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId, format = 'html' } = body

    // التحقق من البيانات المطلوبة
    if (!projectId) {
      return NextResponse.json(
        { error: 'معرف المشروع مطلوب' },
        { status: 400 }
      )
    }

    // جلب المشروع
    const projects = MockDataManager.getPageProjects()
    const project = projects.find(p => p.id === projectId)

    if (!project) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      )
    }

    // جلب عناصر القائمة الرئيسية
    const menuItems = MockDataManager.getMenuItems()

    // توليد HTML
    const htmlContent = generateHTML(project, menuItems)

    // إنشاء استجابة التصدير
    const response = new NextResponse(htmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Content-Disposition': `attachment; filename="${project.name}.html"`,
      },
    })

    return response

  } catch (error) {
    console.error('Error exporting project:', error)
    return NextResponse.json(
      { error: 'خطأ في تصدير المشروع' },
      { status: 500 }
    )
  }
}

// دالة مساعدة لتوليد HTML
function generateHTML(project: any, menuItems: any[]): string {
  const hasMainHeader = project.components.some((c: any) => c.type === 'header')
  
  const headerHTML = hasMainHeader ? `
    <header class="bg-white border-b shadow-sm p-4 sticky top-0 z-50">
      <div class="container mx-auto flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div class="text-xl font-bold text-blue-600">
            منصة أزياء التخرج
          </div>
        </div>
        <nav class="hidden md:flex items-center gap-6">
          ${menuItems.slice(0, 6).map(item => `
            <a href="#" class="text-sm hover:text-blue-600 transition-colors">
              ${item.title_ar || item.title}
            </a>
          `).join('')}
        </nav>
        <div class="flex items-center gap-2">
          <button class="px-4 py-2 text-sm border rounded-lg hover:bg-gray-50">تسجيل الدخول</button>
          <button class="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700">إنشاء حساب</button>
        </div>
      </div>
    </header>
  ` : ''

  const componentsHTML = project.components
    .filter((c: any) => c.type !== 'header')
    .map((component: any) => {
      const style = component.props.style || {}
      const styleString = Object.entries(style)
        .map(([key, value]) => `${kebabCase(key)}: ${value}`)
        .join('; ')

      return `
        <section style="${styleString}" class="component-${component.type}">
          <div class="container mx-auto">
            ${generateComponentContent(component)}
          </div>
        </section>
      `
    }).join('')

  return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${project.settings?.title || project.name}</title>
    <meta name="description" content="${project.settings?.description || project.description}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .component-hero {
            min-height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .component-features {
            padding: 3rem 0;
        }
        
        .component-gallery {
            padding: 3rem 0;
        }
        
        .component-contact {
            padding: 3rem 0;
        }
        
        .component-text {
            padding: 3rem 0;
        }
        
        .component-footer {
            background-color: #374151;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    ${headerHTML}
    
    <main>
        ${componentsHTML}
    </main>
    
    <script>
        // إضافة تفاعلات بسيطة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة بنجاح');
        });
    </script>
</body>
</html>
  `.trim()
}

// دالة مساعدة لتوليد محتوى المكون
function generateComponentContent(component: any): string {
  const content = component.props.content || ''
  
  switch (component.type) {
    case 'hero':
      return `
        <div class="hero-content">
          <h1 class="hero-title">${content}</h1>
          <p class="hero-subtitle">${component.props.subtitle || ''}</p>
        </div>
      `
    
    case 'features':
      return `
        <div class="features-content">
          <h2 style="font-size: 2rem; margin-bottom: 2rem; text-align: center;">${content}</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
            <div style="text-align: center; padding: 2rem;">
              <h3>ميزة رائعة</h3>
              <p>وصف الميزة هنا</p>
            </div>
            <div style="text-align: center; padding: 2rem;">
              <h3>ميزة أخرى</h3>
              <p>وصف الميزة هنا</p>
            </div>
            <div style="text-align: center; padding: 2rem;">
              <h3>ميزة ثالثة</h3>
              <p>وصف الميزة هنا</p>
            </div>
          </div>
        </div>
      `
    
    case 'gallery':
      return `
        <div class="gallery-content">
          <h2 style="font-size: 2rem; margin-bottom: 2rem; text-align: center;">${content}</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
            <div style="background-color: #f3f4f6; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
              صورة 1
            </div>
            <div style="background-color: #f3f4f6; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
              صورة 2
            </div>
            <div style="background-color: #f3f4f6; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
              صورة 3
            </div>
          </div>
        </div>
      `
    
    case 'contact':
      return `
        <div class="contact-content">
          <h2 style="font-size: 2rem; margin-bottom: 2rem; text-align: center;">${content}</h2>
          <form style="max-width: 600px; margin: 0 auto;">
            <div style="margin-bottom: 1rem;">
              <label style="display: block; margin-bottom: 0.5rem;">الاسم</label>
              <input type="text" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
              <label style="display: block; margin-bottom: 0.5rem;">البريد الإلكتروني</label>
              <input type="email" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
              <label style="display: block; margin-bottom: 0.5rem;">الرسالة</label>
              <textarea rows="4" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 4px;"></textarea>
            </div>
            <button type="submit" style="background-color: #3b82f6; color: white; padding: 0.75rem 2rem; border: none; border-radius: 4px; cursor: pointer;">
              إرسال
            </button>
          </form>
        </div>
      `
    
    case 'text':
      return `
        <div class="text-content">
          <div style="max-width: 800px; margin: 0 auto; text-align: center;">
            <p style="font-size: 1.125rem; line-height: 1.8;">${content}</p>
          </div>
        </div>
      `
    
    case 'footer':
      return `
        <div class="footer-content">
          <p>${content}</p>
        </div>
      `
    
    default:
      return `<div>${content}</div>`
  }
}

// دالة مساعدة لتحويل camelCase إلى kebab-case
function kebabCase(str: string): string {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
}
