"use client"

import { useState } from 'react'
import { useTranslation } from '@/hooks/useTranslation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { PageLayout } from '@/components/layouts/PageLayout'
import { 
  GraduationCap, 
  Search, 
  Filter, 
  Heart,
  ShoppingCart,
  Star,
  Eye
} from 'lucide-react'

// بيانات تجريبية للمنتجات
const sampleProducts = [
  {
    id: 1,
    name: 'ثوب التخرج الكلاسيكي',
    description: 'ثوب تخرج أنيق ومريح للمناسبات الرسمية، مصنوع من أجود الخامات',
    category: 'gown',
    price: 299.99,
    rental_price: 99.99,
    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],
    sizes: ['S', 'M', 'L', 'XL', 'XXL'],
    image: '/api/placeholder/300/400',
    rating: 4.8,
    reviews: 124,
    isNew: true
  },
  {
    id: 2,
    name: 'قبعة التخرج التقليدية',
    description: 'قبعة تخرج كلاسيكية مع شرابة، تصميم تقليدي أنيق',
    category: 'cap',
    price: 79.99,
    rental_price: 29.99,
    colors: ['أسود', 'أزرق داكن'],
    sizes: ['واحد'],
    image: '/api/placeholder/300/400',
    rating: 4.6,
    reviews: 89,
    isNew: false
  },
  {
    id: 3,
    name: 'وشاح التخرج المطرز',
    description: 'وشاح أنيق مطرز بتفاصيل ذهبية، يضيف لمسة من الأناقة',
    category: 'stole',
    price: 149.99,
    rental_price: 49.99,
    colors: ['ذهبي', 'فضي', 'أبيض'],
    sizes: ['واحد'],
    image: '/api/placeholder/300/400',
    rating: 4.9,
    reviews: 67,
    isNew: true
  },
  {
    id: 4,
    name: 'شرابة التخرج الملونة',
    description: 'شرابة ملونة لقبعة التخرج، متوفرة بألوان متعددة',
    category: 'tassel',
    price: 19.99,
    rental_price: 9.99,
    colors: ['ذهبي', 'فضي', 'أسود', 'أزرق'],
    sizes: ['واحد'],
    image: '/api/placeholder/300/400',
    rating: 4.5,
    reviews: 156,
    isNew: false
  }
]

const categories = [
  { value: 'all', label: 'جميع المنتجات', icon: '🎓' },
  { value: 'gown', label: 'أثواب التخرج', icon: '👘' },
  { value: 'cap', label: 'قبعات التخرج', icon: '🎩' },
  { value: 'stole', label: 'أوشحة التخرج', icon: '🧣' },
  { value: 'tassel', label: 'شرابات التخرج', icon: '🏷️' }
]

export default function CatalogPage() {
  const { t } = useTranslation()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('name')
  const [favorites, setFavorites] = useState<number[]>([])

  const toggleFavorite = (productId: number) => {
    setFavorites(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }

  const filteredProducts = sampleProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price
      case 'price-high':
        return b.price - a.price
      case 'rating':
        return b.rating - a.rating
      case 'name':
      default:
        return a.name.localeCompare(b.name, 'ar')
    }
  })

  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8">
        {/* Page Title */}
        <div className="text-center mb-8">
          <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            🎓 كتالوج أزياء التخرج
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 arabic-text">
            اكتشف مجموعتنا المتميزة من أزياء التخرج الأنيقة
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="ابحث عن المنتجات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 arabic-text"
              />
            </div>

            {/* Category Filter */}
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="اختر الفئة" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    <span className="flex items-center gap-2 arabic-text">
                      <span>{category.icon}</span>
                      {category.label}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="ترتيب حسب" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">الاسم</SelectItem>
                <SelectItem value="price-low">السعر: من الأقل للأعلى</SelectItem>
                <SelectItem value="price-high">السعر: من الأعلى للأقل</SelectItem>
                <SelectItem value="rating">التقييم</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Products Grid */}
        <div className="product-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {sortedProducts.map((product) => (
            <Card key={product.id} className="group hover:shadow-lg transition-shadow duration-300">
              <CardHeader className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {product.isNew && (
                    <Badge className="absolute top-2 right-2 bg-green-500">
                      جديد
                    </Badge>
                  )}
                  <button
                    onClick={() => toggleFavorite(product.id)}
                    className="absolute top-2 left-2 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:scale-110 transition-transform"
                  >
                    <Heart 
                      className={`h-4 w-4 ${
                        favorites.includes(product.id) 
                          ? 'text-red-500 fill-current' 
                          : 'text-gray-400'
                      }`} 
                    />
                  </button>
                </div>
              </CardHeader>
              
              <CardContent className="p-4">
                <div className="flex items-center gap-1 mb-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(product.rating)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    ({product.reviews})
                  </span>
                </div>

                <CardTitle className="text-lg mb-2 arabic-text line-clamp-2">
                  {product.name}
                </CardTitle>
                
                <CardDescription className="text-sm mb-4 arabic-text line-clamp-2">
                  {product.description}
                </CardDescription>

                <div className="flex flex-wrap gap-1 mb-4">
                  {product.colors.slice(0, 3).map((color, index) => (
                    <Badge key={index} variant="outline" className="text-xs arabic-text">
                      {color}
                    </Badge>
                  ))}
                  {product.colors.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{product.colors.length - 3}
                    </Badge>
                  )}
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="price text-lg font-bold text-blue-600 dark:text-blue-400">
                      {product.rental_price} درهم
                    </div>
                    <div className="price text-sm text-gray-500 line-through">
                      {product.price} درهم
                    </div>
                  </div>
                  <Badge variant="secondary" className="arabic-text">
                    إيجار
                  </Badge>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" className="flex-1 arabic-text">
                    <ShoppingCart className="h-4 w-4 mr-1" />
                    إضافة للسلة
                  </Button>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {sortedProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 arabic-text">
              لم يتم العثور على منتجات
            </h3>
            <p className="text-gray-600 dark:text-gray-300 arabic-text">
              جرب تغيير معايير البحث أو الفلترة
            </p>
          </div>
        )}
    </PageLayout>
  )
}
