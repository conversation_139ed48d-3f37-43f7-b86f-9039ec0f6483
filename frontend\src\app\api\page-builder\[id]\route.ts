import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'

// GET - جلب مشروع واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projects = MockDataManager.getPageProjects()
    const project = projects.find(p => p.id === params.id)

    if (!project) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({ project })

  } catch (error) {
    console.error('Error fetching page project:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب المشروع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مشروع
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const projects = MockDataManager.getPageProjects()
    const projectIndex = projects.findIndex(p => p.id === params.id)

    if (projectIndex === -1) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      )
    }

    // تحديث المشروع
    const updatedProject = {
      ...projects[projectIndex],
      ...body,
      id: params.id, // التأكد من عدم تغيير المعرف
      updatedAt: new Date().toISOString()
    }

    projects[projectIndex] = updatedProject
    MockDataManager.savePageProjects(projects)

    return NextResponse.json({ 
      message: 'تم تحديث المشروع بنجاح',
      project: updatedProject 
    })

  } catch (error) {
    console.error('Error updating page project:', error)
    return NextResponse.json(
      { error: 'خطأ في تحديث المشروع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مشروع واحد
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projects = MockDataManager.getPageProjects()
    const projectIndex = projects.findIndex(p => p.id === params.id)

    if (projectIndex === -1) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      )
    }

    const project = projects[projectIndex]

    // التحقق من حالة النشر
    if (project.isPublished) {
      return NextResponse.json(
        { 
          error: 'لا يمكن حذف المشروع المنشور. يرجى إلغاء نشره أولاً.',
          suggestion: 'unpublish_first'
        },
        { status: 400 }
      )
    }

    // حذف المشروع
    const deletedProject = projects.splice(projectIndex, 1)[0]
    MockDataManager.savePageProjects(projects)

    return NextResponse.json({ 
      message: `تم حذف المشروع "${deletedProject.name}" بنجاح`,
      project: deletedProject 
    })

  } catch (error) {
    console.error('Error deleting page project:', error)
    return NextResponse.json(
      { error: 'خطأ في حذف المشروع' },
      { status: 500 }
    )
  }
}
