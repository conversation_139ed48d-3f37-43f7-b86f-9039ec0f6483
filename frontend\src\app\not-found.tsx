"use client"

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ThemeToggle } from '@/components/theme-toggle'
import { LanguageToggle } from '@/components/language-toggle'
import { useTranslation } from '@/hooks/useTranslation'
import { ErrorLayout } from '@/components/layouts/PageLayout'
import { 
  Home, 
  ArrowLeft, 
  Search, 
  RefreshCw,
  AlertTriangle,
  GraduationCap
} from 'lucide-react'

export default function NotFound() {
  const router = useRouter()
  const { t } = useTranslation()
  const [countdown, setCountdown] = useState(10)
  const [isRedirecting, setIsRedirecting] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setIsRedirecting(true)
          router.push('/')
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [router])

  const handleGoHome = () => {
    setIsRedirecting(true)
    router.push('/')
  }

  const handleGoBack = () => {
    router.back()
  }

  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <GraduationCap className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Toqs
            </h1>
          </div>
          <div className="flex items-center gap-2">
            <LanguageToggle />
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-16 flex items-center justify-center min-h-[calc(100vh-80px)]">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-8 text-center">
            {/* Error Icon */}
            <div className="mb-8">
              <div className="mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-12 w-12 text-red-600 dark:text-red-400" />
              </div>
              <h1 className="text-6xl font-bold text-gray-900 dark:text-white mb-2">
                404
              </h1>
              <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-300 arabic-text">
                الصفحة غير موجودة
              </h2>
            </div>

            {/* Error Message */}
            <div className="mb-8">
              <p className="text-gray-600 dark:text-gray-400 text-lg mb-4 arabic-text">
                عذراً، لا يمكننا العثور على الصفحة التي تبحث عنها.
              </p>
              <p className="text-gray-500 dark:text-gray-500 arabic-text">
                قد تكون الصفحة قد تم نقلها أو حذفها أو أن الرابط غير صحيح.
              </p>
            </div>

            {/* Auto Redirect Notice */}
            {!isRedirecting && (
              <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-blue-700 dark:text-blue-300 arabic-text">
                  سيتم توجيهك تلقائياً إلى الصفحة الرئيسية خلال {countdown} ثانية
                </p>
              </div>
            )}

            {/* Loading State */}
            {isRedirecting && (
              <div className="mb-8 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-center justify-center gap-2">
                  <RefreshCw className="h-4 w-4 animate-spin text-green-600 dark:text-green-400" />
                  <p className="text-green-700 dark:text-green-300 arabic-text">
                    جاري التوجيه...
                  </p>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={handleGoHome}
                className="flex items-center gap-2"
                disabled={isRedirecting}
              >
                <Home className="h-4 w-4" />
                <span className="arabic-text">الصفحة الرئيسية</span>
              </Button>

              <Button 
                variant="outline" 
                onClick={handleGoBack}
                className="flex items-center gap-2"
                disabled={isRedirecting}
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="arabic-text">العودة للخلف</span>
              </Button>

              <Button 
                variant="outline" 
                onClick={handleRefresh}
                className="flex items-center gap-2"
                disabled={isRedirecting}
              >
                <RefreshCw className="h-4 w-4" />
                <span className="arabic-text">إعادة تحميل</span>
              </Button>
            </div>

            {/* Additional Help */}
            <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 arabic-text">
                ماذا يمكنك أن تفعل؟
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Search className="h-6 w-6 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                  <p className="text-gray-700 dark:text-gray-300 arabic-text">
                    ابحث عن المنتجات في الكتالوج
                  </p>
                  <Link href="/catalog" className="text-blue-600 dark:text-blue-400 hover:underline">
                    تصفح الكتالوج
                  </Link>
                </div>

                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <GraduationCap className="h-6 w-6 text-green-600 dark:text-green-400 mx-auto mb-2" />
                  <p className="text-gray-700 dark:text-gray-300 arabic-text">
                    تعرف على خدماتنا
                  </p>
                  <Link href="/about" className="text-blue-600 dark:text-blue-400 hover:underline">
                    من نحن
                  </Link>
                </div>

                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Home className="h-6 w-6 text-purple-600 dark:text-purple-400 mx-auto mb-2" />
                  <p className="text-gray-700 dark:text-gray-300 arabic-text">
                    ابدأ من الصفحة الرئيسية
                  </p>
                  <Link href="/" className="text-blue-600 dark:text-blue-400 hover:underline">
                    الصفحة الرئيسية
                  </Link>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
