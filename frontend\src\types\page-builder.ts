// أنواع بيانات بناء الصفحات الذكية

export type ComponentType = 
  | 'text' 
  | 'heading' 
  | 'image' 
  | 'button' 
  | 'card' 
  | 'hero' 
  | 'navbar' 
  | 'footer' 
  | 'gallery' 
  | 'form' 
  | 'video' 
  | 'testimonial' 
  | 'pricing' 
  | 'features' 
  | 'contact' 
  | 'blog' 
  | 'product' 
  | 'container' 
  | 'grid' 
  | 'flex'

export type TemplateCategory = 
  | 'landing' 
  | 'business' 
  | 'ecommerce' 
  | 'blog' 
  | 'portfolio' 
  | 'education' 
  | 'restaurant' 
  | 'medical' 
  | 'real-estate' 
  | 'fashion' 
  | 'technology'

export type GenerationMode = 'ai' | 'template' | 'manual'

export interface ComponentPosition {
  x: number // الموضع الأفقي
  y: number // الموضع العمودي
  z?: number // ترتيب العمق
}

export interface ComponentSize {
  width: number | string // العرض
  height: number | string // الارتفاع
  minWidth?: number | string // الحد الأدنى للعرض
  minHeight?: number | string // الحد الأدنى للارتفاع
  maxWidth?: number | string // الحد الأقصى للعرض
  maxHeight?: number | string // الحد الأقصى للارتفاع
}

export interface ComponentStyle {
  backgroundColor?: string // لون الخلفية
  color?: string // لون النص
  fontSize?: string // حجم الخط
  fontFamily?: string // نوع الخط
  fontWeight?: string // وزن الخط
  textAlign?: 'left' | 'center' | 'right' | 'justify' // محاذاة النص
  padding?: string // الحشو الداخلي
  margin?: string // الهامش الخارجي
  border?: string // الحدود
  borderRadius?: string // انحناء الحدود
  boxShadow?: string // الظل
  opacity?: number // الشفافية
  transform?: string // التحويل
  transition?: string // الانتقال
  [key: string]: any // خصائص إضافية
}

export interface ComponentProps {
  id?: string // معرف المكون
  className?: string // فئة CSS
  style?: ComponentStyle // الأنماط
  content?: string // المحتوى النصي
  src?: string // مصدر الصورة/الفيديو
  alt?: string // النص البديل
  href?: string // الرابط
  target?: string // هدف الرابط
  placeholder?: string // النص التوضيحي
  required?: boolean // مطلوب
  disabled?: boolean // معطل
  onClick?: string // حدث النقر
  [key: string]: any // خصائص إضافية
}

export interface PageComponent {
  id: string // معرف المكون
  type: ComponentType // نوع المكون
  name: string // اسم المكون
  props: ComponentProps // خصائص المكون
  children?: PageComponent[] // المكونات الفرعية
  position: ComponentPosition // الموضع
  size: ComponentSize // الحجم
  isLocked?: boolean // مقفل للتحرير
  isVisible?: boolean // مرئي
  parentId?: string // معرف المكون الأب
  order?: number // ترتيب المكون
  responsive?: {
    mobile?: Partial<ComponentProps & ComponentPosition & ComponentSize>
    tablet?: Partial<ComponentProps & ComponentPosition & ComponentSize>
    desktop?: Partial<ComponentProps & ComponentPosition & ComponentSize>
  } // الاستجابة للشاشات
}

export interface PageTemplate {
  id: string // معرف القالب
  name: string // اسم القالب
  nameAr: string // الاسم بالعربية
  nameEn?: string // الاسم بالإنجليزية
  nameFr?: string // الاسم بالفرنسية
  description: string // وصف القالب
  category: TemplateCategory // فئة القالب
  components: PageComponent[] // مكونات القالب
  preview: string // صورة المعاينة
  thumbnail: string // الصورة المصغرة
  isAIGenerated: boolean // مولد بالذكاء الاصطناعي
  isPremium: boolean // قالب مدفوع
  tags: string[] // العلامات
  createdAt: string // تاريخ الإنشاء
  updatedAt: string // تاريخ التحديث
  createdBy?: string // منشئ القالب
  usageCount: number // عدد مرات الاستخدام
  rating?: number // التقييم
  metadata?: {
    colors: string[] // الألوان المستخدمة
    fonts: string[] // الخطوط المستخدمة
    layout: string // نوع التخطيط
    responsive: boolean // متجاوب
    [key: string]: any
  }
}

export interface PageProject {
  id: string // معرف المشروع
  name: string // اسم المشروع
  description?: string // وصف المشروع
  components: PageComponent[] // مكونات الصفحة
  templateId?: string // معرف القالب المستخدم
  generationMode: GenerationMode // طريقة الإنشاء
  settings: {
    title: string // عنوان الصفحة
    description: string // وصف الصفحة
    keywords: string[] // الكلمات المفتاحية
    language: 'ar' | 'en' | 'fr' // اللغة
    direction: 'rtl' | 'ltr' // اتجاه النص
    favicon?: string // أيقونة الموقع
    customCSS?: string // CSS مخصص
    customJS?: string // JavaScript مخصص
  }
  isPublished: boolean // منشور
  publishedUrl?: string // رابط النشر
  createdAt: string // تاريخ الإنشاء
  updatedAt: string // تاريخ التحديث
  createdBy: string // منشئ المشروع
  lastEditedBy?: string // آخر محرر
  version: number // إصدار المشروع
  history?: PageProjectHistory[] // تاريخ التعديلات
}

export interface PageProjectHistory {
  id: string // معرف التاريخ
  projectId: string // معرف المشروع
  version: number // رقم الإصدار
  components: PageComponent[] // مكونات الإصدار
  description?: string // وصف التغيير
  createdAt: string // تاريخ الإنشاء
  createdBy: string // منشئ الإصدار
}

export interface ComponentLibraryItem {
  id: string // معرف العنصر
  name: string // اسم العنصر
  nameAr: string // الاسم بالعربية
  type: ComponentType // نوع المكون
  category: string // فئة المكون
  description: string // وصف المكون
  icon: string // أيقونة المكون
  preview: string // صورة المعاينة
  defaultProps: ComponentProps // الخصائص الافتراضية
  defaultSize: ComponentSize // الحجم الافتراضي
  isCustom: boolean // مكون مخصص
  isPremium: boolean // مكون مدفوع
  tags: string[] // العلامات
  usageCount: number // عدد مرات الاستخدام
}

export interface AIGenerationRequest {
  prompt: string // الوصف المطلوب
  language: 'ar' | 'en' | 'fr' // اللغة
  category?: TemplateCategory // الفئة المطلوبة
  style?: string // النمط المطلوب
  colors?: string[] // الألوان المفضلة
  includeImages?: boolean // تضمين الصور
  includeText?: boolean // تضمين النصوص
  pageType?: string // نوع الصفحة
  targetAudience?: string // الجمهور المستهدف
  businessType?: string // نوع العمل
  includeMainHeader?: boolean // تضمين هيدر القائمة الرئيسية
  mainMenuItems?: any[] // عناصر القائمة الرئيسية
  modelId?: string // معرف نموذج الذكاء الاصطناعي
}

export interface AIGenerationResponse {
  success: boolean // حالة النجاح
  components?: PageComponent[] // المكونات المولدة
  template?: PageTemplate // القالب المولد
  suggestions?: string[] // اقتراحات للتحسين
  error?: string // رسالة الخطأ
  metadata?: {
    tokensUsed: number // الرموز المستخدمة
    generationTime: number // وقت التوليد
    modelUsed: string // النموذج المستخدم
    [key: string]: any
  }
}

export interface PageBuilderState {
  currentProject?: PageProject // المشروع الحالي
  selectedComponent?: string // المكون المحدد
  selectedComponents: string[] // المكونات المحددة
  clipboard?: PageComponent[] // الحافظة
  history: PageComponent[][] // تاريخ التعديلات
  historyIndex: number // فهرس التاريخ
  zoom: number // مستوى التكبير
  gridSize: number // حجم الشبكة
  showGrid: boolean // إظهار الشبكة
  snapToGrid: boolean // الالتصاق بالشبكة
  previewMode: boolean // وضع المعاينة
  devicePreview: 'desktop' | 'tablet' | 'mobile' // معاينة الجهاز
  isDirty: boolean // تم التعديل
  isLoading: boolean // جاري التحميل
  error?: string // رسالة الخطأ
}

export interface PageBuilderAction {
  type: string // نوع الإجراء
  payload?: any // البيانات
  timestamp: string // الطابع الزمني
}

// أنواع للاستجابات
export interface PageTemplatesResponse {
  templates: PageTemplate[]
  total: number
  page?: number
  limit?: number
  categories: TemplateCategory[]
}

export interface PageProjectsResponse {
  projects: PageProject[]
  total: number
  page?: number
  limit?: number
}

export interface ComponentLibraryResponse {
  components: ComponentLibraryItem[]
  total: number
  categories: string[]
}

// أنواع للطلبات
export interface CreateProjectRequest {
  name: string
  description?: string
  templateId?: string
  generationMode: GenerationMode
  settings?: Partial<PageProject['settings']>
}

export interface UpdateProjectRequest {
  name?: string
  description?: string
  components?: PageComponent[]
  settings?: Partial<PageProject['settings']>
  isPublished?: boolean
}

export interface PublishProjectRequest {
  projectId: string
  domain?: string
  subdomain?: string
  customDomain?: string
}

export interface ExportProjectRequest {
  projectId: string
  format: 'html' | 'react' | 'vue' | 'angular'
  includeAssets: boolean
  minify: boolean
}
