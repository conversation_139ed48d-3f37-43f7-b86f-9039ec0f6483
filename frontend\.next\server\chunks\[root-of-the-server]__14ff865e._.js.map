{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/mockData.ts"], "sourcesContent": ["// بيانات وهمية للتطوير والاختبار\nimport { AIModel, AISubModel, ModelActivity } from '@/types/ai-models'\nimport { PageTemplate, PageProject, ComponentLibraryItem, PageComponent } from '@/types/page-builder'\n\nexport interface MockPage {\n  id: string\n  slug: string\n  is_published: boolean\n  author_id: string\n  featured_image?: string\n  created_at: string\n  updated_at: string\n  page_content: MockPageContent[]\n  profiles?: {\n    full_name: string\n  }\n}\n\nexport interface MockPageContent {\n  id: string\n  page_id: string\n  language: 'ar' | 'en' | 'fr'\n  title: string\n  content: string\n  meta_description?: string\n  meta_keywords?: string\n}\n\nexport interface MockMenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockCategory {\n  id: string\n  name_ar: string\n  name_en?: string\n  name_fr?: string\n  slug: string\n  icon?: string\n  description?: string\n  is_active: boolean\n  order_index: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockProduct {\n  id: string\n  name: string\n  description: string\n  category: string // تغيير من union type إلى string للمرونة\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n  features?: string[]\n  specifications?: Record<string, any>\n}\n\nexport interface MockSchool {\n  id: string\n  admin_id?: string\n  name: string\n  name_en?: string\n  name_fr?: string\n  address?: string\n  city?: string\n  phone?: string\n  email?: string\n  website?: string\n  logo_url?: string\n  graduation_date?: string\n  student_count: number\n  is_active: boolean\n  settings?: Record<string, any>\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockOrder {\n  id: string\n  order_number: string\n  customer_id: string\n  customer_name: string\n  customer_email: string\n  customer_phone?: string\n  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'\n  items: MockOrderItem[]\n  subtotal: number\n  tax: number\n  shipping_cost: number\n  total: number\n  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'\n  payment_method?: string\n  shipping_address: {\n    street: string\n    city: string\n    state: string\n    postal_code: string\n    country: string\n  }\n  tracking_number?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  delivery_date?: string\n  school_id?: string\n  school_name?: string\n}\n\nexport interface MockOrderItem {\n  id: string\n  order_id: string\n  product_id: string\n  product_name: string\n  product_image: string\n  category: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  customizations?: {\n    color?: string\n    size?: string\n    embroidery?: string\n    special_requests?: string\n  }\n}\n\n// بيانات وهمية للصفحات\nexport const mockPages: MockPage[] = [\n  {\n    id: '1',\n    slug: 'about-us',\n    is_published: true,\n    author_id: 'admin-1',\n    featured_image: '/images/about-hero.jpg',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '1-ar',\n        page_id: '1',\n        language: 'ar',\n        title: 'من نحن',\n        content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n        meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n        meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n      },\n      {\n        id: '1-en',\n        page_id: '1',\n        language: 'en',\n        title: 'About Us',\n        content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n        meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n        meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n      }\n    ]\n  },\n  {\n    id: '2',\n    slug: 'services',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '2-ar',\n        page_id: '2',\n        language: 'ar',\n        title: 'خدماتنا',\n        content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n        meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n        meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n      }\n    ]\n  },\n  {\n    id: '3',\n    slug: 'contact',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-17T11:00:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '3-ar',\n        page_id: '3',\n        language: 'ar',\n        title: 'اتصل بنا',\n        content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n        meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n        meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n      }\n    ]\n  }\n]\n\n// بيانات وهمية للقوائم\nexport const mockMenuItems: MockMenuItem[] = [\n  {\n    id: '1',\n    title_ar: 'الرئيسية',\n    title_en: 'Home',\n    title_fr: 'Accueil',\n    slug: 'home',\n    icon: 'Home',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    title_ar: 'من نحن',\n    title_en: 'About Us',\n    title_fr: 'À propos',\n    slug: 'about',\n    icon: 'Info',\n    order_index: 2,\n    is_active: true,\n    target_type: 'page',\n    target_value: '1',\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    title_ar: 'خدماتنا',\n    title_en: 'Services',\n    title_fr: 'Services',\n    slug: 'services',\n    icon: 'Settings',\n    order_index: 3,\n    is_active: true,\n    target_type: 'page',\n    target_value: '2',\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    title_ar: 'المنتجات',\n    title_en: 'Products',\n    title_fr: 'Produits',\n    slug: 'products',\n    icon: 'Package',\n    order_index: 4,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products',\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    title_ar: 'تأجير الأزياء',\n    title_en: 'Rental',\n    title_fr: 'Location',\n    slug: 'rental',\n    parent_id: '4',\n    icon: 'Calendar',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=rental',\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  },\n  {\n    id: '6',\n    title_ar: 'بيع الأزياء',\n    title_en: 'Sales',\n    title_fr: 'Vente',\n    slug: 'sales',\n    parent_id: '4',\n    icon: 'ShoppingCart',\n    order_index: 2,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=sale',\n    created_at: '2024-01-15T10:25:00Z',\n    updated_at: '2024-01-15T10:25:00Z'\n  },\n  {\n    id: '7',\n    title_ar: 'الكتالوج',\n    title_en: 'Catalog',\n    title_fr: 'Catalogue',\n    slug: 'catalog',\n    icon: 'Grid3X3',\n    order_index: 5,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/catalog',\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:30:00Z'\n  },\n  {\n    id: '8',\n    title_ar: 'اتصل بنا',\n    title_en: 'Contact',\n    title_fr: 'Contact',\n    slug: 'contact',\n    icon: 'Phone',\n    order_index: 6,\n    is_active: true,\n    target_type: 'page',\n    target_value: '3',\n    created_at: '2024-01-15T10:35:00Z',\n    updated_at: '2024-01-15T10:35:00Z'\n  }\n]\n\n// بيانات وهمية للفئات\nexport const mockCategories: MockCategory[] = [\n  {\n    id: '1',\n    name_ar: 'أثواب التخرج',\n    name_en: 'Graduation Gowns',\n    name_fr: 'Robes de Graduation',\n    slug: 'gown',\n    icon: '👘',\n    description: 'أثواب التخرج الأكاديمية التقليدية',\n    is_active: true,\n    order_index: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    name_ar: 'قبعات التخرج',\n    name_en: 'Graduation Caps',\n    name_fr: 'Chapeaux de Graduation',\n    slug: 'cap',\n    icon: '🎩',\n    description: 'قبعات التخرج الأكاديمية',\n    is_active: true,\n    order_index: 2,\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    name_ar: 'شرابات التخرج',\n    name_en: 'Graduation Tassels',\n    name_fr: 'Glands de Graduation',\n    slug: 'tassel',\n    icon: '🏷️',\n    description: 'شرابات التخرج الملونة',\n    is_active: true,\n    order_index: 3,\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    name_ar: 'أوشحة التخرج',\n    name_en: 'Graduation Stoles',\n    name_fr: 'Étoles de Graduation',\n    slug: 'stole',\n    icon: '🧣',\n    description: 'أوشحة التخرج المميزة',\n    is_active: true,\n    order_index: 4,\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    name_ar: 'القلانس الأكاديمية',\n    name_en: 'Academic Hoods',\n    name_fr: 'Capuches Académiques',\n    slug: 'hood',\n    icon: '🎓',\n    description: 'القلانس الأكاديمية للدرجات العليا',\n    is_active: true,\n    order_index: 5,\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  }\n]\n\n// بيانات وهمية للمنتجات\nexport const mockProducts: MockProduct[] = [\n  {\n    id: '1',\n    name: 'ثوب التخرج الكلاسيكي',\n    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n    category: 'gown',\n    price: 299.99,\n    rental_price: 99.99,\n    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],\n    sizes: ['S', 'M', 'L', 'XL', 'XXL'],\n    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],\n    stock_quantity: 25,\n    is_available: true,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    rating: 4.8,\n    reviews_count: 42,\n    features: ['مقاوم للتجاعيد', 'قابل للغسيل', 'خامة عالية الجودة'],\n    specifications: {\n      material: 'بوليستر عالي الجودة',\n      weight: '0.8 كيلو',\n      care: 'غسيل جاف أو غسيل عادي'\n    }\n  },\n  {\n    id: '2',\n    name: 'قبعة التخرج التقليدية',\n    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n    category: 'cap',\n    price: 79.99,\n    rental_price: 29.99,\n    colors: ['أسود', 'أزرق داكن'],\n    sizes: ['One Size'],\n    images: ['/images/products/cap-traditional-1.jpg'],\n    stock_quantity: 50,\n    is_available: true,\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    rating: 4.6,\n    reviews_count: 28,\n    features: ['مقاس واحد يناسب الجميع', 'شرابة ذهبية', 'تصميم تقليدي'],\n    specifications: {\n      material: 'قطن مخلوط',\n      tassel_color: 'ذهبي',\n      adjustable: 'نعم'\n    }\n  },\n  {\n    id: '3',\n    name: 'وشاح التخرج المطرز',\n    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n    category: 'stole',\n    price: 149.99,\n    rental_price: 49.99,\n    colors: ['أبيض مع ذهبي', 'أزرق مع فضي', 'أحمر مع ذهبي'],\n    sizes: ['One Size'],\n    images: ['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],\n    stock_quantity: 15,\n    is_available: true,\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-25T10:15:00Z',\n    rating: 4.9,\n    reviews_count: 18,\n    features: ['تطريز يدوي', 'خيوط ذهبية', 'تصميم فاخر'],\n    specifications: {\n      material: 'حرير طبيعي',\n      embroidery: 'خيوط ذهبية وفضية',\n      length: '150 سم'\n    }\n  },\n  {\n    id: '4',\n    name: 'شرابة التخرج الذهبية',\n    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n    category: 'tassel',\n    price: 39.99,\n    rental_price: 15.99,\n    colors: ['ذهبي', 'فضي', 'أزرق', 'أحمر'],\n    sizes: ['One Size'],\n    images: ['/images/products/tassel-gold-1.jpg'],\n    stock_quantity: 100,\n    is_available: true,\n    created_at: '2024-01-18T14:00:00Z',\n    updated_at: '2024-01-26T09:30:00Z',\n    rating: 4.7,\n    reviews_count: 35,\n    features: ['خيوط عالية الجودة', 'ألوان ثابتة', 'سهل التركيب'],\n    specifications: {\n      material: 'خيوط حريرية',\n      length: '23 سم',\n      attachment: 'مشبك معدني'\n    }\n  },\n  {\n    id: '5',\n    name: 'قلنسوة الدكتوراه الفاخرة',\n    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n    category: 'hood',\n    price: 199.99,\n    rental_price: 79.99,\n    colors: ['أسود مع ذهبي', 'أزرق مع فضي'],\n    sizes: ['M', 'L', 'XL'],\n    images: ['/images/products/hood-doctorate-1.jpg', '/images/products/hood-doctorate-2.jpg'],\n    stock_quantity: 8,\n    is_available: true,\n    created_at: '2024-01-19T16:00:00Z',\n    updated_at: '2024-01-27T12:00:00Z',\n    rating: 5.0,\n    reviews_count: 12,\n    features: ['تصميم أكاديمي أصيل', 'خامة فاخرة', 'مناسب للدكتوراه'],\n    specifications: {\n      material: 'مخمل عالي الجودة',\n      lining: 'حرير ملون',\n      academic_level: 'دكتوراه'\n    }\n  }\n]\n\n// بيانات وهمية للمدارس\nexport const mockSchools: MockSchool[] = [\n  {\n    id: '1',\n    admin_id: 'admin-school-1',\n    name: 'جامعة الإمارات العربية المتحدة',\n    name_en: 'United Arab Emirates University',\n    name_fr: 'Université des Émirats Arabes Unis',\n    address: 'شارع الجامعة، العين',\n    city: 'العين',\n    phone: '+971-3-713-5000',\n    email: '<EMAIL>',\n    website: 'https://www.uaeu.ac.ae',\n    logo_url: '/images/schools/uaeu-logo.png',\n    graduation_date: '2024-06-15',\n    student_count: 14500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',\n      dress_code: 'formal',\n      photography_allowed: true\n    },\n    created_at: '2024-01-10T08:00:00Z',\n    updated_at: '2024-01-25T10:30:00Z'\n  },\n  {\n    id: '2',\n    admin_id: 'admin-school-2',\n    name: 'الجامعة الأمريكية في الشارقة',\n    name_en: 'American University of Sharjah',\n    name_fr: 'Université Américaine de Sharjah',\n    address: 'شارع الجامعة، الشارقة',\n    city: 'الشارقة',\n    phone: '+971-6-515-5555',\n    email: '<EMAIL>',\n    website: 'https://www.aus.edu',\n    logo_url: '/images/schools/aus-logo.png',\n    graduation_date: '2024-05-20',\n    student_count: 6200,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مسرح الجامعة',\n      dress_code: 'academic',\n      photography_allowed: true\n    },\n    created_at: '2024-01-12T09:15:00Z',\n    updated_at: '2024-01-28T14:20:00Z'\n  },\n  {\n    id: '3',\n    admin_id: 'admin-school-3',\n    name: 'جامعة زايد',\n    name_en: 'Zayed University',\n    name_fr: 'Université Zayed',\n    address: 'شارع الشيخ زايد، دبي',\n    city: 'دبي',\n    phone: '+971-4-402-1111',\n    email: '<EMAIL>',\n    website: 'https://www.zu.ac.ae',\n    logo_url: '/images/schools/zu-logo.png',\n    graduation_date: '2024-06-10',\n    student_count: 9800,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مركز المؤتمرات',\n      dress_code: 'formal',\n      photography_allowed: false\n    },\n    created_at: '2024-01-15T11:00:00Z',\n    updated_at: '2024-02-01T16:45:00Z'\n  },\n  {\n    id: '4',\n    admin_id: 'admin-school-4',\n    name: 'كلية الإمارات للتكنولوجيا',\n    name_en: 'Emirates Institute of Technology',\n    name_fr: 'Institut de Technologie des Émirats',\n    address: 'المنطقة الأكاديمية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-401-4000',\n    email: '<EMAIL>',\n    website: 'https://www.eit.ac.ae',\n    logo_url: '/images/schools/eit-logo.png',\n    graduation_date: '2024-07-05',\n    student_count: 3500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'القاعة الرئيسية',\n      dress_code: 'business',\n      photography_allowed: true\n    },\n    created_at: '2024-01-18T13:30:00Z',\n    updated_at: '2024-02-05T09:15:00Z'\n  },\n  {\n    id: '5',\n    admin_id: 'admin-school-5',\n    name: 'معهد أبوظبي للتعليم التقني',\n    name_en: 'Abu Dhabi Technical Institute',\n    name_fr: 'Institut Technique d\\'Abu Dhabi',\n    address: 'المنطقة الصناعية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-505-2000',\n    email: '<EMAIL>',\n    website: 'https://www.adti.ac.ae',\n    graduation_date: '2024-06-25',\n    student_count: 2800,\n    is_active: false,\n    settings: {\n      graduation_ceremony_location: 'مركز التدريب',\n      dress_code: 'casual',\n      photography_allowed: true\n    },\n    created_at: '2024-01-20T15:45:00Z',\n    updated_at: '2024-02-10T12:00:00Z'\n  }\n]\n\n// بيانات وهمية للطلبات\nexport const mockOrders: MockOrder[] = [\n  {\n    id: '1',\n    order_number: 'GT-240120-001',\n    customer_id: 'student-1',\n    customer_name: 'أحمد محمد علي',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-50-123-4567',\n    status: 'in_production',\n    items: [\n      {\n        id: '1',\n        order_id: '1',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'أسود',\n          size: 'L',\n          embroidery: 'أحمد علي - بكالوريوس هندسة'\n        }\n      },\n      {\n        id: '2',\n        order_id: '1',\n        product_id: '2',\n        product_name: 'قبعة التخرج الأكاديمية',\n        product_image: '/images/products/cap-academic-1.jpg',\n        category: 'cap',\n        quantity: 1,\n        unit_price: 89.99,\n        total_price: 89.99,\n        customizations: {\n          color: 'أسود',\n          size: 'M'\n        }\n      }\n    ],\n    subtotal: 389.98,\n    tax: 19.50,\n    shipping_cost: 25.00,\n    total: 434.48,\n    payment_status: 'paid',\n    payment_method: 'credit_card',\n    shipping_address: {\n      street: 'شارع الجامعة، مبنى 12، شقة 304',\n      city: 'العين',\n      state: 'أبوظبي',\n      postal_code: '17666',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-001-2024',\n    notes: 'يرجى التسليم قبل حفل التخرج',\n    created_at: '2024-01-20T10:30:00Z',\n    updated_at: '2024-01-22T14:15:00Z',\n    delivery_date: '2024-02-15T00:00:00Z',\n    school_id: '1',\n    school_name: 'جامعة الإمارات العربية المتحدة'\n  },\n  {\n    id: '2',\n    order_number: 'GT-**********',\n    customer_id: 'student-2',\n    customer_name: 'فاطمة سالم الزهراني',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-56-789-0123',\n    status: 'delivered',\n    items: [\n      {\n        id: '3',\n        order_id: '2',\n        product_id: '3',\n        product_name: 'ثوب التخرج المميز',\n        product_image: '/images/products/gown-premium-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 399.99,\n        total_price: 399.99,\n        customizations: {\n          color: 'أزرق داكن',\n          size: 'M',\n          embroidery: 'فاطمة الزهراني - ماجستير إدارة أعمال'\n        }\n      }\n    ],\n    subtotal: 399.99,\n    tax: 20.00,\n    shipping_cost: 30.00,\n    total: 449.99,\n    payment_status: 'paid',\n    payment_method: 'bank_transfer',\n    shipping_address: {\n      street: 'شارع الكورنيش، برج الإمارات، الطابق 15',\n      city: 'الشارقة',\n      state: 'الشارقة',\n      postal_code: '27272',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-002-2024',\n    created_at: '2024-01-21T09:15:00Z',\n    updated_at: '2024-01-25T16:30:00Z',\n    delivery_date: '2024-01-28T00:00:00Z',\n    school_id: '2',\n    school_name: 'الجامعة الأمريكية في الشارقة'\n  },\n  {\n    id: '3',\n    order_number: 'GT-**********',\n    customer_id: 'student-3',\n    customer_name: 'خالد عبدالله المنصوري',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-52-456-7890',\n    status: 'pending',\n    items: [\n      {\n        id: '4',\n        order_id: '3',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'بورجوندي',\n          size: 'XL'\n        }\n      },\n      {\n        id: '5',\n        order_id: '3',\n        product_id: '4',\n        product_name: 'وشاح التخرج المطرز',\n        product_image: '/images/products/stole-embroidered-1.jpg',\n        category: 'stole',\n        quantity: 1,\n        unit_price: 149.99,\n        total_price: 149.99,\n        customizations: {\n          color: 'ذهبي',\n          embroidery: 'كلية الهندسة'\n        }\n      }\n    ],\n    subtotal: 449.98,\n    tax: 22.50,\n    shipping_cost: 25.00,\n    total: 497.48,\n    payment_status: 'pending',\n    shipping_address: {\n      street: 'شارع الشيخ زايد، مجمع دبي الأكاديمي',\n      city: 'دبي',\n      state: 'دبي',\n      postal_code: '391186',\n      country: 'الإمارات العربية المتحدة'\n    },\n    created_at: '2024-01-22T14:45:00Z',\n    updated_at: '2024-01-22T14:45:00Z',\n    school_id: '3',\n    school_name: 'جامعة زايد'\n  }\n]\n\n// مساعدات للتعامل مع البيانات الوهمية\nexport class MockDataManager {\n  private static getStorageKey(type: 'pages' | 'menuItems' | 'products' | 'categories' | 'schools' | 'orders'): string {\n    return `mockData_${type}`\n  }\n\n  static getPages(): MockPage[] {\n    if (typeof window === 'undefined') return mockPages\n\n    const stored = localStorage.getItem(this.getStorageKey('pages'))\n    return stored ? JSON.parse(stored) : mockPages\n  }\n\n  static getMenuItems(): MockMenuItem[] {\n    if (typeof window === 'undefined') return mockMenuItems\n\n    const stored = localStorage.getItem(this.getStorageKey('menuItems'))\n    return stored ? JSON.parse(stored) : mockMenuItems\n  }\n\n  static getProducts(): MockProduct[] {\n    if (typeof window === 'undefined') return mockProducts\n\n    const stored = localStorage.getItem(this.getStorageKey('products'))\n    return stored ? JSON.parse(stored) : mockProducts\n  }\n\n  static getCategories(): MockCategory[] {\n    if (typeof window === 'undefined') return mockCategories\n\n    const stored = localStorage.getItem(this.getStorageKey('categories'))\n    return stored ? JSON.parse(stored) : mockCategories\n  }\n\n  static getSchools(): MockSchool[] {\n    if (typeof window === 'undefined') return mockSchools\n\n    const stored = localStorage.getItem(this.getStorageKey('schools'))\n    return stored ? JSON.parse(stored) : mockSchools\n  }\n\n  static getOrders(): MockOrder[] {\n    if (typeof window === 'undefined') return mockOrders\n\n    const stored = localStorage.getItem(this.getStorageKey('orders'))\n    return stored ? JSON.parse(stored) : mockOrders\n  }\n\n  static savePages(pages: MockPage[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('pages'), JSON.stringify(pages))\n    }\n  }\n\n  static saveMenuItems(items: MockMenuItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('menuItems'), JSON.stringify(items))\n    }\n  }\n\n  static saveProducts(products: MockProduct[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('products'), JSON.stringify(products))\n    }\n  }\n\n  static saveCategories(categories: MockCategory[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('categories'), JSON.stringify(categories))\n    }\n  }\n\n  static saveSchools(schools: MockSchool[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('schools'), JSON.stringify(schools))\n    }\n  }\n\n  static saveOrders(orders: MockOrder[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('orders'), JSON.stringify(orders))\n    }\n  }\n\n  static generateId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 11)\n  }\n\n  // مسح جميع البيانات المحفوظة (للاختبار)\n  static clearAllData(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('mockAIModels')\n      localStorage.removeItem('mockModelActivities')\n      localStorage.removeItem('mockPages')\n      localStorage.removeItem('mockPageTemplates')\n      localStorage.removeItem('mockPageProjects')\n      localStorage.removeItem('mockComponentLibrary')\n    }\n  }\n\n  static generateOrderNumber(): string {\n    const date = new Date()\n    const year = date.getFullYear().toString().slice(-2)\n    const month = (date.getMonth() + 1).toString().padStart(2, '0')\n    const day = date.getDate().toString().padStart(2, '0')\n    const orders = this.getOrders()\n    const todayOrders = orders.filter(order =>\n      order.created_at.startsWith(`${date.getFullYear()}-${month}-${day}`)\n    )\n    const orderCount = (todayOrders.length + 1).toString().padStart(3, '0')\n    return `GT-${year}${month}${day}-${orderCount}`\n  }\n\n  // إدارة نماذج الذكاء الاصطناعي\n  static getAIModels(): AIModel[] {\n    if (typeof window === 'undefined') return this.defaultAIModels\n\n    const stored = localStorage.getItem('mockAIModels')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultAIModels\n  }\n\n  static saveAIModels(models: AIModel[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockAIModels', JSON.stringify(models))\n    }\n  }\n\n  static getModelActivities(): ModelActivity[] {\n    if (typeof window === 'undefined') return this.defaultModelActivities\n\n    const stored = localStorage.getItem('mockModelActivities')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultModelActivities\n  }\n\n  static saveModelActivities(activities: ModelActivity[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockModelActivities', JSON.stringify(activities))\n    }\n  }\n\n  // إدارة قوالب الصفحات\n  static getPageTemplates(): PageTemplate[] {\n    if (typeof window === 'undefined') return this.defaultPageTemplates\n\n    const stored = localStorage.getItem('mockPageTemplates')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultPageTemplates\n  }\n\n  static savePageTemplates(templates: PageTemplate[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockPageTemplates', JSON.stringify(templates))\n    }\n  }\n\n  static getPageProjects(): PageProject[] {\n    if (typeof window === 'undefined') return this.defaultPageProjects\n\n    const stored = localStorage.getItem('mockPageProjects')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultPageProjects\n  }\n\n  static savePageProjects(projects: PageProject[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockPageProjects', JSON.stringify(projects))\n    }\n  }\n\n  static getComponentLibrary(): ComponentLibraryItem[] {\n    if (typeof window === 'undefined') return this.defaultComponentLibrary\n\n    const stored = localStorage.getItem('mockComponentLibrary')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultComponentLibrary\n  }\n\n  static saveComponentLibrary(components: ComponentLibraryItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockComponentLibrary', JSON.stringify(components))\n    }\n  }\n\n  // البيانات الافتراضية لنماذج الذكاء الاصطناعي (فارغة للبداية)\n  static defaultAIModels: AIModel[] = []\n\n  static defaultModelActivities: ModelActivity[] = []\n\n  // البيانات الافتراضية لقوالب الصفحات\n  static defaultPageTemplates: PageTemplate[] = [\n    {\n      id: 'template-landing-1',\n      name: 'Landing Page - Modern',\n      nameAr: 'صفحة هبوط - عصرية',\n      nameEn: 'Landing Page - Modern',\n      nameFr: 'Page d\\'atterrissage - Moderne',\n      description: 'قالب صفحة هبوط عصرية مع تصميم جذاب',\n      category: 'landing',\n      components: [\n        {\n          id: 'hero-1',\n          type: 'hero',\n          name: 'Hero Section',\n          props: {\n            content: 'مرحباً بكم في منصة أزياء التخرج',\n            style: { backgroundColor: '#1F2937', color: '#FFFFFF' }\n          },\n          position: { x: 0, y: 0 },\n          size: { width: '100%', height: '500px' },\n          isVisible: true\n        }\n      ],\n      preview: '/images/templates/landing-modern.jpg',\n      thumbnail: '/images/templates/landing-modern-thumb.jpg',\n      isAIGenerated: false,\n      isPremium: false,\n      tags: ['landing', 'modern', 'business'],\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-01T00:00:00Z',\n      usageCount: 45,\n      rating: 4.8,\n      metadata: {\n        colors: ['#1F2937', '#FFFFFF', '#3B82F6'],\n        fonts: ['Inter', 'Cairo'],\n        layout: 'single-page',\n        responsive: true\n      }\n    }\n  ]\n\n  // البيانات الافتراضية لمشاريع الصفحات\n  static defaultPageProjects: PageProject[] = [\n    {\n      id: 'project-1',\n      name: 'موقع أزياء التخرج الرئيسي',\n      description: 'الموقع الرئيسي لعرض منتجات أزياء التخرج',\n      components: [],\n      templateId: 'template-landing-1',\n      generationMode: 'template',\n      settings: {\n        title: 'أزياء التخرج - منصة مغربية متخصصة',\n        description: 'أول منصة مغربية لتأجير وبيع أزياء التخرج',\n        keywords: ['أزياء التخرج', 'تأجير', 'المغرب'],\n        language: 'ar',\n        direction: 'rtl'\n      },\n      isPublished: false,\n      createdAt: '2024-01-15T00:00:00Z',\n      updatedAt: '2024-01-20T10:30:00Z',\n      createdBy: 'admin-1',\n      version: 1\n    }\n  ]\n\n  // البيانات الافتراضية لمكتبة المكونات\n  static defaultComponentLibrary: ComponentLibraryItem[] = [\n    {\n      id: 'comp-hero',\n      name: 'Hero Section',\n      nameAr: 'قسم البطل',\n      type: 'hero',\n      category: 'layout',\n      description: 'قسم رئيسي جذاب في أعلى الصفحة',\n      icon: 'Layout',\n      preview: '/images/components/hero-preview.jpg',\n      defaultProps: {\n        content: 'عنوان رئيسي جذاب',\n        style: { backgroundColor: '#1F2937', color: '#FFFFFF' }\n      },\n      defaultSize: { width: '100%', height: '500px' },\n      isCustom: false,\n      isPremium: false,\n      tags: ['layout', 'header', 'hero'],\n      usageCount: 156\n    },\n    {\n      id: 'comp-button',\n      name: 'Button',\n      nameAr: 'زر',\n      type: 'button',\n      category: 'interactive',\n      description: 'زر تفاعلي قابل للتخصيص',\n      icon: 'MousePointer',\n      preview: '/images/components/button-preview.jpg',\n      defaultProps: {\n        content: 'انقر هنا',\n        style: { backgroundColor: '#3B82F6', color: '#FFFFFF' }\n      },\n      defaultSize: { width: '120px', height: '40px' },\n      isCustom: false,\n      isPremium: false,\n      tags: ['interactive', 'button', 'cta'],\n      usageCount: 234\n    }\n  ]\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;AAoJ1B,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;CACD;AAGM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,iBAAiC;IAC5C;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAa;SAAW;QACzC,OAAO;YAAC;YAAK;YAAK;YAAK;YAAM;SAAM;QACnC,QAAQ;YAAC;YAAuC;SAAsC;QACtF,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAkB;YAAe;SAAoB;QAChE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;SAAY;QAC7B,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAyC;QAClD,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAA0B;YAAe;SAAe;QACnE,gBAAgB;YACd,UAAU;YACV,cAAc;YACd,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;YAAe;SAAe;QACvD,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;YAA4C;SAA2C;QAChG,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAc;YAAc;SAAa;QACpD,gBAAgB;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAO;YAAQ;SAAO;QACvC,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAqC;QAC9C,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAqB;YAAe;SAAc;QAC7D,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;SAAc;QACvC,OAAO;YAAC;YAAK;YAAK;SAAK;QACvB,QAAQ;YAAC;YAAyC;SAAwC;QAC1F,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAsB;YAAc;SAAkB;QACjE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,gBAAgB;QAClB;IACF;CACD;AAGM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,aAAa;IACf;CACD;AAGM,MAAM;IACX,OAAe,cAAc,IAA8E,EAAU;QACnH,OAAO,CAAC,SAAS,EAAE,MAAM;IAC3B;IAEA,OAAO,WAAuB;QAC5B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,eAA+B;QACpC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,cAA6B;QAClC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,gBAAgC;QACrC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,aAA2B;QAChC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,YAAyB;QAC9B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,UAAU,KAAiB,EAAQ;QACxC,uCAAmC;;QAEnC;IACF;IAEA,OAAO,cAAc,KAAqB,EAAQ;QAChD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAa,QAAuB,EAAQ;QACjD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,eAAe,UAA0B,EAAQ;QACtD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,YAAY,OAAqB,EAAQ;QAC9C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,WAAW,MAAmB,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAqB;QAC1B,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACzE;IAEA,wCAAwC;IACxC,OAAO,eAAqB;QAC1B,uCAAmC;;QAOnC;IACF;IAEA,OAAO,sBAA8B;QACnC,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,WAAW,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;QAClD,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAC3D,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAClD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,QAChC,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG,KAAK,WAAW,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;QAErE,MAAM,aAAa,CAAC,YAAY,MAAM,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QACnE,OAAO,CAAC,GAAG,EAAE,OAAO,QAAQ,IAAI,CAAC,EAAE,YAAY;IACjD;IAEA,+BAA+B;IAC/B,OAAO,cAAyB;QAC9B,wCAAmC,OAAO,IAAI,CAAC,eAAe;;QAE9D,MAAM;IAKR;IAEA,OAAO,aAAa,MAAiB,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,qBAAsC;QAC3C,wCAAmC,OAAO,IAAI,CAAC,sBAAsB;;QAErE,MAAM;IAKR;IAEA,OAAO,oBAAoB,UAA2B,EAAQ;QAC5D,uCAAmC;;QAEnC;IACF;IAEA,sBAAsB;IACtB,OAAO,mBAAmC;QACxC,wCAAmC,OAAO,IAAI,CAAC,oBAAoB;;QAEnE,MAAM;IAKR;IAEA,OAAO,kBAAkB,SAAyB,EAAQ;QACxD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,kBAAiC;QACtC,wCAAmC,OAAO,IAAI,CAAC,mBAAmB;;QAElE,MAAM;IAKR;IAEA,OAAO,iBAAiB,QAAuB,EAAQ;QACrD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,sBAA8C;QACnD,wCAAmC,OAAO,IAAI,CAAC,uBAAuB;;QAEtE,MAAM;IAKR;IAEA,OAAO,qBAAqB,UAAkC,EAAQ;QACpE,uCAAmC;;QAEnC;IACF;IAEA,8DAA8D;IAC9D,OAAO,kBAA6B,EAAE,CAAA;IAEtC,OAAO,yBAA0C,EAAE,CAAA;IAEnD,qCAAqC;IACrC,OAAO,uBAAuC;QAC5C;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,UAAU;YACV,YAAY;gBACV;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,OAAO;wBACL,SAAS;wBACT,OAAO;4BAAE,iBAAiB;4BAAW,OAAO;wBAAU;oBACxD;oBACA,UAAU;wBAAE,GAAG;wBAAG,GAAG;oBAAE;oBACvB,MAAM;wBAAE,OAAO;wBAAQ,QAAQ;oBAAQ;oBACvC,WAAW;gBACb;aACD;YACD,SAAS;YACT,WAAW;YACX,eAAe;YACf,WAAW;YACX,MAAM;gBAAC;gBAAW;gBAAU;aAAW;YACvC,WAAW;YACX,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,UAAU;gBACR,QAAQ;oBAAC;oBAAW;oBAAW;iBAAU;gBACzC,OAAO;oBAAC;oBAAS;iBAAQ;gBACzB,QAAQ;gBACR,YAAY;YACd;QACF;KACD,CAAA;IAED,sCAAsC;IACtC,OAAO,sBAAqC;QAC1C;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,YAAY,EAAE;YACd,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBACR,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAC;oBAAgB;oBAAS;iBAAS;gBAC7C,UAAU;gBACV,WAAW;YACb;YACA,aAAa;YACb,WAAW;YACX,WAAW;YACX,WAAW;YACX,SAAS;QACX;KACD,CAAA;IAED,sCAAsC;IACtC,OAAO,0BAAkD;QACvD;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,cAAc;gBACZ,SAAS;gBACT,OAAO;oBAAE,iBAAiB;oBAAW,OAAO;gBAAU;YACxD;YACA,aAAa;gBAAE,OAAO;gBAAQ,QAAQ;YAAQ;YAC9C,UAAU;YACV,WAAW;YACX,MAAM;gBAAC;gBAAU;gBAAU;aAAO;YAClC,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,cAAc;gBACZ,SAAS;gBACT,OAAO;oBAAE,iBAAiB;oBAAW,OAAO;gBAAU;YACxD;YACA,aAAa;gBAAE,OAAO;gBAAS,QAAQ;YAAO;YAC9C,UAAU;YACV,WAAW;YACX,MAAM;gBAAC;gBAAe;gBAAU;aAAM;YACtC,YAAY;QACd;KACD,CAAA;AACH", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/ai-models/test/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { MockDataManager } from '@/lib/mockData'\nimport { ModelTestRequest, ModelTestResponse } from '@/types/ai-models'\n\n// POST - اختبار نموذج ذكاء اصطناعي\nexport async function POST(request: NextRequest) {\n  try {\n    const body: ModelTestRequest = await request.json()\n    const { modelId, subModelId, prompt, settings } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!modelId || !prompt) {\n      return NextResponse.json(\n        { error: 'معرف النموذج والنص المطلوب مطلوبان' },\n        { status: 400 }\n      )\n    }\n\n    // جلب النموذج\n    const models = MockDataManager.getAIModels()\n    const model = models.find(m => m.id === modelId)\n\n    if (!model) {\n      return NextResponse.json(\n        { error: 'النموذج غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    if (!model.isActive) {\n      return NextResponse.json(\n        { error: 'النموذج غير نشط' },\n        { status: 400 }\n      )\n    }\n\n    // التحقق من النموذج الفرعي إذا تم تحديده\n    let subModel = null\n    if (subModelId) {\n      subModel = model.subModels.find(sm => sm.id === subModelId)\n      if (!subModel) {\n        return NextResponse.json(\n          { error: 'النموذج الفرعي غير موجود' },\n          { status: 404 }\n        )\n      }\n      if (!subModel.isActive) {\n        return NextResponse.json(\n          { error: 'النموذج الفرعي غير نشط' },\n          { status: 400 }\n        )\n      }\n    }\n\n    // محاكاة اختبار النموذج\n    const startTime = Date.now()\n    \n    // محاكاة وقت الاستجابة بناءً على نوع النموذج\n    const baseResponseTime = model.provider === 'openai' ? 1000 : \n                           model.provider === 'anthropic' ? 1500 :\n                           model.provider === 'google' ? 800 : 1200\n    \n    const responseTime = baseResponseTime + Math.floor(Math.random() * 1000)\n    \n    // محاكاة نسبة نجاح عالية (95%)\n    const success = Math.random() > 0.05\n    \n    // محاكاة عدد الرموز المستخدمة\n    const promptTokens = Math.ceil(prompt.length / 4) // تقدير تقريبي\n    const completionTokens = success ? Math.floor(Math.random() * 500) + 50 : 0\n    const totalTokens = promptTokens + completionTokens\n    \n    // حساب التكلفة التقديرية\n    const pricing = subModel?.pricing || { inputTokens: 0.001, outputTokens: 0.002, currency: 'USD' }\n    const cost = success ? \n      (promptTokens / 1000 * pricing.inputTokens) + (completionTokens / 1000 * pricing.outputTokens) : \n      0\n\n    // إنشاء الاستجابة\n    const testResponse: ModelTestResponse = {\n      success,\n      responseTime,\n      tokensUsed: totalTokens,\n      cost,\n      metadata: {\n        model: model.name,\n        subModel: subModel?.name,\n        provider: model.provider,\n        promptTokens,\n        completionTokens,\n        settings: { ...model.settings, ...settings }\n      }\n    }\n\n    if (success) {\n      // محاكاة استجابة النموذج\n      const responses = [\n        'مرحباً! أنا مساعد ذكي جاهز لمساعدتك في أي استفسار.',\n        'شكراً لك على اختبار النموذج. يبدو أن كل شيء يعمل بشكل صحيح.',\n        'هذا اختبار ناجح للنموذج. يمكنني مساعدتك في مختلف المهام.',\n        'النموذج يعمل بكفاءة عالية ومستعد للاستخدام.',\n        'تم اختبار النموذج بنجاح. جودة الاستجابة ممتازة.'\n      ]\n      \n      testResponse.response = responses[Math.floor(Math.random() * responses.length)]\n    } else {\n      testResponse.error = 'فشل في الاتصال بالنموذج. يرجى التحقق من إعدادات API.'\n    }\n\n    // تحديث إحصائيات النموذج\n    const modelIndex = models.findIndex(m => m.id === modelId)\n    if (modelIndex !== -1) {\n      const updatedModel = models[modelIndex]\n      \n      // تحديث آخر اختبار\n      updatedModel.lastTestedAt = new Date().toISOString()\n      updatedModel.testResult = {\n        success,\n        responseTime,\n        error: success ? undefined : testResponse.error\n      }\n      \n      // تحديث الحالة\n      updatedModel.status = success ? 'active' : 'error'\n      \n      // تحديث إحصائيات الاستخدام\n      if (success) {\n        updatedModel.usage.totalRequests += 1\n        updatedModel.usage.totalTokens += totalTokens\n        updatedModel.usage.totalCost += cost\n        updatedModel.usage.lastUsed = new Date().toISOString()\n        \n        // تحديث متوسط وقت الاستجابة\n        const totalResponseTime = updatedModel.usage.averageResponseTime * (updatedModel.usage.totalRequests - 1) + responseTime\n        updatedModel.usage.averageResponseTime = Math.round(totalResponseTime / updatedModel.usage.totalRequests)\n        \n        // تحديث معدل النجاح\n        const successfulRequests = Math.round(updatedModel.usage.successRate * (updatedModel.usage.totalRequests - 1) / 100) + 1\n        updatedModel.usage.successRate = Math.round((successfulRequests / updatedModel.usage.totalRequests) * 100)\n      }\n      \n      updatedModel.updatedAt = new Date().toISOString()\n      models[modelIndex] = updatedModel\n      MockDataManager.saveAIModels(models)\n    }\n\n    // إضافة نشاط\n    const activities = MockDataManager.getModelActivities()\n    activities.push({\n      id: MockDataManager.generateId(),\n      modelId,\n      subModelId,\n      type: 'test',\n      description: `اختبار النموذج: ${success ? 'نجح' : 'فشل'}`,\n      details: {\n        prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),\n        settings: { ...model.settings, ...settings }\n      },\n      timestamp: new Date().toISOString(),\n      duration: responseTime,\n      tokensUsed: totalTokens,\n      cost,\n      success,\n      errorMessage: success ? undefined : testResponse.error\n    })\n    MockDataManager.saveModelActivities(activities)\n\n    return NextResponse.json(testResponse)\n\n  } catch (error) {\n    console.error('Error testing AI model:', error)\n    return NextResponse.json(\n      { error: 'خطأ في اختبار النموذج' },\n      { status: 500 }\n    )\n  }\n}\n\n// GET - جلب نتائج الاختبارات السابقة\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const modelId = searchParams.get('model_id')\n    const limit = parseInt(searchParams.get('limit') || '20')\n\n    // جلب الأنشطة\n    let activities = MockDataManager.getModelActivities()\n\n    // فلترة أنشطة الاختبار\n    activities = activities.filter(activity => activity.type === 'test')\n\n    // فلترة حسب النموذج إذا تم تحديده\n    if (modelId) {\n      activities = activities.filter(activity => activity.modelId === modelId)\n    }\n\n    // ترتيب حسب التاريخ (الأحدث أولاً)\n    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())\n\n    // تطبيق الحد الأقصى\n    activities = activities.slice(0, limit)\n\n    // إحصائيات الاختبارات\n    const allTestActivities = MockDataManager.getModelActivities().filter(a => a.type === 'test')\n    const stats = {\n      total: allTestActivities.length,\n      successful: allTestActivities.filter(a => a.success).length,\n      failed: allTestActivities.filter(a => !a.success).length,\n      averageResponseTime: allTestActivities.length > 0 \n        ? Math.round(allTestActivities.reduce((sum, a) => sum + (a.duration || 0), 0) / allTestActivities.length)\n        : 0,\n      totalCost: allTestActivities.reduce((sum, a) => sum + (a.cost || 0), 0),\n      byModel: allTestActivities.reduce((acc, activity) => {\n        const modelId = activity.modelId\n        if (!acc[modelId]) {\n          acc[modelId] = { total: 0, successful: 0, failed: 0 }\n        }\n        acc[modelId].total += 1\n        if (activity.success) {\n          acc[modelId].successful += 1\n        } else {\n          acc[modelId].failed += 1\n        }\n        return acc\n      }, {} as Record<string, { total: number, successful: number, failed: number }>)\n    }\n\n    return NextResponse.json({\n      tests: activities,\n      stats,\n      total: activities.length\n    })\n\n  } catch (error) {\n    console.error('Error fetching test results:', error)\n    return NextResponse.json(\n      { error: 'خطأ في جلب نتائج الاختبارات' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAIO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAyB,MAAM,QAAQ,IAAI;QACjD,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;QAElD,8BAA8B;QAC9B,IAAI,CAAC,WAAW,CAAC,QAAQ;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAExC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,yCAAyC;QACzC,IAAI,WAAW;QACf,IAAI,YAAY;YACd,WAAW,MAAM,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;YAChD,IAAI,CAAC,UAAU;gBACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA2B,GACpC;oBAAE,QAAQ;gBAAI;YAElB;YACA,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAyB,GAClC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,wBAAwB;QACxB,MAAM,YAAY,KAAK,GAAG;QAE1B,6CAA6C;QAC7C,MAAM,mBAAmB,MAAM,QAAQ,KAAK,WAAW,OAChC,MAAM,QAAQ,KAAK,cAAc,OACjC,MAAM,QAAQ,KAAK,WAAW,MAAM;QAE3D,MAAM,eAAe,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAEnE,+BAA+B;QAC/B,MAAM,UAAU,KAAK,MAAM,KAAK;QAEhC,8BAA8B;QAC9B,MAAM,eAAe,KAAK,IAAI,CAAC,OAAO,MAAM,GAAG,GAAG,eAAe;;QACjE,MAAM,mBAAmB,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,KAAK;QAC1E,MAAM,cAAc,eAAe;QAEnC,yBAAyB;QACzB,MAAM,UAAU,UAAU,WAAW;YAAE,aAAa;YAAO,cAAc;YAAO,UAAU;QAAM;QAChG,MAAM,OAAO,UACX,AAAC,eAAe,OAAO,QAAQ,WAAW,GAAK,mBAAmB,OAAO,QAAQ,YAAY,GAC7F;QAEF,kBAAkB;QAClB,MAAM,eAAkC;YACtC;YACA;YACA,YAAY;YACZ;YACA,UAAU;gBACR,OAAO,MAAM,IAAI;gBACjB,UAAU,UAAU;gBACpB,UAAU,MAAM,QAAQ;gBACxB;gBACA;gBACA,UAAU;oBAAE,GAAG,MAAM,QAAQ;oBAAE,GAAG,QAAQ;gBAAC;YAC7C;QACF;QAEA,IAAI,SAAS;YACX,yBAAyB;YACzB,MAAM,YAAY;gBAChB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,aAAa,QAAQ,GAAG,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;QACjF,OAAO;YACL,aAAa,KAAK,GAAG;QACvB;QAEA,yBAAyB;QACzB,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,eAAe,CAAC,GAAG;YACrB,MAAM,eAAe,MAAM,CAAC,WAAW;YAEvC,mBAAmB;YACnB,aAAa,YAAY,GAAG,IAAI,OAAO,WAAW;YAClD,aAAa,UAAU,GAAG;gBACxB;gBACA;gBACA,OAAO,UAAU,YAAY,aAAa,KAAK;YACjD;YAEA,eAAe;YACf,aAAa,MAAM,GAAG,UAAU,WAAW;YAE3C,2BAA2B;YAC3B,IAAI,SAAS;gBACX,aAAa,KAAK,CAAC,aAAa,IAAI;gBACpC,aAAa,KAAK,CAAC,WAAW,IAAI;gBAClC,aAAa,KAAK,CAAC,SAAS,IAAI;gBAChC,aAAa,KAAK,CAAC,QAAQ,GAAG,IAAI,OAAO,WAAW;gBAEpD,4BAA4B;gBAC5B,MAAM,oBAAoB,aAAa,KAAK,CAAC,mBAAmB,GAAG,CAAC,aAAa,KAAK,CAAC,aAAa,GAAG,CAAC,IAAI;gBAC5G,aAAa,KAAK,CAAC,mBAAmB,GAAG,KAAK,KAAK,CAAC,oBAAoB,aAAa,KAAK,CAAC,aAAa;gBAExG,oBAAoB;gBACpB,MAAM,qBAAqB,KAAK,KAAK,CAAC,aAAa,KAAK,CAAC,WAAW,GAAG,CAAC,aAAa,KAAK,CAAC,aAAa,GAAG,CAAC,IAAI,OAAO;gBACvH,aAAa,KAAK,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC,AAAC,qBAAqB,aAAa,KAAK,CAAC,aAAa,GAAI;YACxG;YAEA,aAAa,SAAS,GAAG,IAAI,OAAO,WAAW;YAC/C,MAAM,CAAC,WAAW,GAAG;YACrB,wHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAC/B;QAEA,aAAa;QACb,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;QACrD,WAAW,IAAI,CAAC;YACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B;YACA;YACA,MAAM;YACN,aAAa,CAAC,gBAAgB,EAAE,UAAU,QAAQ,OAAO;YACzD,SAAS;gBACP,QAAQ,OAAO,SAAS,CAAC,GAAG,OAAO,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,EAAE;gBACpE,UAAU;oBAAE,GAAG,MAAM,QAAQ;oBAAE,GAAG,QAAQ;gBAAC;YAC7C;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;YACV,YAAY;YACZ;YACA;YACA,cAAc,UAAU,YAAY,aAAa,KAAK;QACxD;QACA,wHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,cAAc;QACd,IAAI,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;QAEnD,uBAAuB;QACvB,aAAa,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK;QAE7D,kCAAkC;QAClC,IAAI,SAAS;YACX,aAAa,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,OAAO,KAAK;QAClE;QAEA,mCAAmC;QACnC,WAAW,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAEzF,oBAAoB;QACpB,aAAa,WAAW,KAAK,CAAC,GAAG;QAEjC,sBAAsB;QACtB,MAAM,oBAAoB,wHAAA,CAAA,kBAAe,CAAC,kBAAkB,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACtF,MAAM,QAAQ;YACZ,OAAO,kBAAkB,MAAM;YAC/B,YAAY,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;YAC3D,QAAQ,kBAAkB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO,EAAE,MAAM;YACxD,qBAAqB,kBAAkB,MAAM,GAAG,IAC5C,KAAK,KAAK,CAAC,kBAAkB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,GAAG,KAAK,kBAAkB,MAAM,IACtG;YACJ,WAAW,kBAAkB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG;YACrE,SAAS,kBAAkB,MAAM,CAAC,CAAC,KAAK;gBACtC,MAAM,UAAU,SAAS,OAAO;gBAChC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACjB,GAAG,CAAC,QAAQ,GAAG;wBAAE,OAAO;wBAAG,YAAY;wBAAG,QAAQ;oBAAE;gBACtD;gBACA,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI;gBACtB,IAAI,SAAS,OAAO,EAAE;oBACpB,GAAG,CAAC,QAAQ,CAAC,UAAU,IAAI;gBAC7B,OAAO;oBACL,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI;gBACzB;gBACA,OAAO;YACT,GAAG,CAAC;QACN;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP;YACA,OAAO,WAAW,MAAM;QAC1B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}