'use client'

import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { DashboardLayout } from '@/components/layouts/PageLayout'
import { UserRole } from '@/types/auth'
import Link from 'next/link'
import { ArrowLeft, Home, Brain } from 'lucide-react'

export default function TestAIModelsPage() {
  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <DashboardLayout
        title="إدارة مقدمي خدمات الذكاء الاصطناعي"
        description="إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم"
      >
        {/* شريط التنقل العلوي */}
        <div className="flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
          <div className="flex items-center gap-4">
            <Link
              href="/dashboard/admin"
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="arabic-text">العودة للوحة التحكم</span>
            </Link>
            <div className="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              <Home className="h-4 w-4" />
              <span className="arabic-text">الصفحة الرئيسية</span>
            </Link>
          </div>

          <div className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            <span className="font-semibold text-gray-900 dark:text-white arabic-text">نماذج الذكاء الاصطناعي</span>
          </div>
        </div>

        {/* محتوى الاختبار */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border">
          <h2 className="text-xl font-bold mb-4 arabic-text">اختبار التحديث</h2>
          <p className="text-gray-600 dark:text-gray-400 arabic-text">
            تم تحديث الصفحة بنجاح مع إضافة القائمة الرئيسية وروابط التنقل.
          </p>
          
          <div className="mt-6 space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm arabic-text">القائمة الرئيسية موحدة</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm arabic-text">رابط العودة للوحة التحكم</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm arabic-text">رابط الصفحة الرئيسية</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm arabic-text">تصميم متجاوب ومحسن</span>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
