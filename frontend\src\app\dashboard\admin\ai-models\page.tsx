'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@/types/auth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { DashboardLayout } from '@/components/layouts/PageLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { toast } from 'sonner'
import {
  Brain,
  Plus,
  Activity,
  ArrowLeft,
  Home,
  Settings,
  Zap
} from 'lucide-react'

export default function AIModelsPage() {
  const { user, profile } = useAuth()
  const [settingsOpen, setSettingsOpen] = useState(false)

  const handleTest = () => {
    toast.success('تم اختبار الصفحة بنجاح!')
  }

  const handleSettings = () => {
    setSettingsOpen(true)
  }

  const handleAddModel = () => {
    toast.success('سيتم فتح نموذج إضافة نموذج جديد...', {
      description: 'إضافة مقدم خدمة ذكاء اصطناعي جديد'
    })
  }

  const handleHealthCheck = () => {
    toast.loading('جاري فحص صحة النماذج...', {
      description: 'يرجى الانتظار'
    })

    // محاكاة فحص الصحة
    setTimeout(() => {
      toast.success('تم فحص جميع النماذج بنجاح!', {
        description: '✅ 5 نماذج تعمل، ⚠️ 2 تحذيرات، ❌ 0 لا تعمل'
      })
    }, 2000)
  }

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <DashboardLayout
        title="إدارة مقدمي خدمات الذكاء الاصطناعي"
        description="إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم"
      >
        {/* شريط التنقل العلوي */}
        <div className="flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
          <div className="flex items-center gap-4">
            <Link
              href="/dashboard/admin"
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="arabic-text">العودة للوحة التحكم</span>
            </Link>
            <div className="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              <Home className="h-4 w-4" />
              <span className="arabic-text">الصفحة الرئيسية</span>
            </Link>
          </div>

          <div className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            <span className="font-semibold text-gray-900 dark:text-white arabic-text">نماذج الذكاء الاصطناعي</span>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <Brain className="h-5 w-5" />
                إدارة نماذج الذكاء الاصطناعي
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-400 arabic-text">
                  تم تحديث الصفحة بنجاح مع إضافة القائمة الرئيسية وروابط التنقل المحسنة.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">القائمة الرئيسية</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">موحدة عبر المنصة</p>
                  </div>
                  
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">روابط التنقل</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">سهولة الوصول</p>
                  </div>
                  
                  <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">تصميم متجاوب</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">جميع الأجهزة</p>
                  </div>
                  
                  <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">الوضع الليلي</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">دعم كامل</p>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-2 pt-4">
                  <Button onClick={handleAddModel}>
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة نموذج
                  </Button>
                  <Button variant="outline" onClick={handleSettings}>
                    <Settings className="h-4 w-4 mr-2" />
                    إعدادات النماذج
                  </Button>
                  <Button variant="outline" onClick={handleHealthCheck}>
                    <Activity className="h-4 w-4 mr-2" />
                    فحص الصحة
                  </Button>
                  <Button variant="outline" onClick={handleTest}>
                    <Zap className="h-4 w-4 mr-2" />
                    اختبار الصفحة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* معلومات إضافية */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text">معلومات التحديث</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm arabic-text">حالة الصفحة</span>
                  <span className="text-sm font-medium text-green-600">✅ تعمل بنجاح</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm arabic-text">القائمة الرئيسية</span>
                  <span className="text-sm font-medium text-green-600">✅ موحدة</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm arabic-text">روابط التنقل</span>
                  <span className="text-sm font-medium text-green-600">✅ تعمل</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm arabic-text">التصميم المتجاوب</span>
                  <span className="text-sm font-medium text-green-600">✅ محسن</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Dialog إعدادات النماذج */}
        <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="arabic-text flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إعدادات النماذج
              </DialogTitle>
              <DialogDescription className="arabic-text">
                تكوين وإدارة إعدادات نماذج الذكاء الاصطناعي
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-3">
                <h4 className="text-sm font-medium arabic-text">الإعدادات العامة</h4>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start arabic-text">
                    <Brain className="h-4 w-4 mr-2" />
                    إدارة مقدمي الخدمة
                  </Button>
                  <Button variant="outline" className="w-full justify-start arabic-text">
                    <Activity className="h-4 w-4 mr-2" />
                    مراقبة الأداء
                  </Button>
                  <Button variant="outline" className="w-full justify-start arabic-text">
                    <Zap className="h-4 w-4 mr-2" />
                    اختبار الاتصال
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="text-sm font-medium arabic-text">إعدادات متقدمة</h4>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start arabic-text">
                    <Settings className="h-4 w-4 mr-2" />
                    تكوين API Keys
                  </Button>
                  <Button variant="outline" className="w-full justify-start arabic-text">
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة مقدم جديد
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setSettingsOpen(false)}>
                إغلاق
              </Button>
              <Button onClick={() => {
                toast.success('تم حفظ الإعدادات بنجاح!')
                setSettingsOpen(false)
              }}>
                حفظ
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
