'use client'

import React from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@/types/auth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { DashboardLayout } from '@/components/layouts/PageLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import {
  Brain,
  Plus,
  Activity,
  ArrowLeft,
  Home
} from 'lucide-react'

export default function AIModelsPage() {
  const { user, profile } = useAuth()

  const handleTest = () => {
    toast.success('تم اختبار الصفحة بنجاح!')
  }

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <DashboardLayout
        title="إدارة مقدمي خدمات الذكاء الاصطناعي"
        description="إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم"
      >
        {/* شريط التنقل العلوي */}
        <div className="flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
          <div className="flex items-center gap-4">
            <Link
              href="/dashboard/admin"
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="arabic-text">العودة للوحة التحكم</span>
            </Link>
            <div className="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              <Home className="h-4 w-4" />
              <span className="arabic-text">الصفحة الرئيسية</span>
            </Link>
          </div>

          <div className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            <span className="font-semibold text-gray-900 dark:text-white arabic-text">نماذج الذكاء الاصطناعي</span>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <Brain className="h-5 w-5" />
                إدارة نماذج الذكاء الاصطناعي
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-400 arabic-text">
                  تم تحديث الصفحة بنجاح مع إضافة القائمة الرئيسية وروابط التنقل المحسنة.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">القائمة الرئيسية</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">موحدة عبر المنصة</p>
                  </div>
                  
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">روابط التنقل</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">سهولة الوصول</p>
                  </div>
                  
                  <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">تصميم متجاوب</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">جميع الأجهزة</p>
                  </div>
                  
                  <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">الوضع الليلي</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">دعم كامل</p>
                  </div>
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button onClick={handleTest}>
                    <Plus className="h-4 w-4 mr-2" />
                    اختبار الصفحة
                  </Button>
                  <Button variant="outline">
                    <Activity className="h-4 w-4 mr-2" />
                    إعدادات النماذج
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* معلومات إضافية */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text">معلومات التحديث</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm arabic-text">حالة الصفحة</span>
                  <span className="text-sm font-medium text-green-600">✅ تعمل بنجاح</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm arabic-text">القائمة الرئيسية</span>
                  <span className="text-sm font-medium text-green-600">✅ موحدة</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm arabic-text">روابط التنقل</span>
                  <span className="text-sm font-medium text-green-600">✅ تعمل</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm arabic-text">التصميم المتجاوب</span>
                  <span className="text-sm font-medium text-green-600">✅ محسن</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
